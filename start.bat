@echo off
chcp 65001 >nul
title 办公辅助系统

echo 🚀 启动办公辅助系统...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 请安装Python 3.7+
    pause
    exit /b 1
)

REM 检查依赖
echo 📦 检查依赖...
pip show pywebview >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 正在安装依赖...
    pip install -r requirements.txt
)

REM 启动应用
echo ✅ 启动应用程序...
python main.py

if errorlevel 1 (
    echo.
    echo ❌ 程序异常退出
    pause
)
