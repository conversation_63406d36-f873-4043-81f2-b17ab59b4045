#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
干净的打包脚本 - 生成无控制台输出的exe
"""

import os
import sys
import subprocess
import shutil

def clean_build():
    """清理构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"清理目录: {dir_name}")
            shutil.rmtree(dir_name)
    
    # 清理.spec文件
    for file in os.listdir('.'):
        if file.endswith('.spec'):
            print(f"清理文件: {file}")
            os.remove(file)

def build_clean_exe():
    """构建干净的exe文件"""
    print("开始构建干净的exe文件...")
    
    # 检查必要文件
    if not os.path.exists('main_release.py'):
        print("错误: main_release.py 文件不存在")
        return False
    
    if not os.path.exists('web'):
        print("错误: web 目录不存在")
        return False
    
    # PyInstaller命令 - 生成无控制台的干净exe
    cmd = [
        'pyinstaller',
        '--onefile',           # 单文件
        '--windowed',          # 无控制台窗口
        '--noconsole',         # 确保无控制台
        '--name=办公辅助系统',    # 程序名
        '--add-data=web;web',  # 添加web目录
        '--hidden-import=openpyxl',
        '--hidden-import=requests',
        '--hidden-import=webview',
        '--hidden-import=apis',
        '--hidden-import=cmm',
        '--hidden-import=sqlite3_util',
        '--clean',             # 清理临时文件
        '--optimize=2',        # 优化字节码
        'main_release.py'      # 使用发布版本
    ]
    
    try:
        print("执行打包命令...")
        subprocess.run(cmd, check=True)
        print("构建成功！")
        
        # 检查输出文件
        exe_path = os.path.join('dist', '办公辅助系统.exe')
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"输出文件: {exe_path}")
            print(f"文件大小: {size_mb:.1f} MB")
            print("这是一个干净的exe文件，运行时不会显示控制台窗口")
            return True
        else:
            print("警告: 未找到输出文件")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"构建失败: {e}")
        return False
    except FileNotFoundError:
        print("错误: PyInstaller 未找到，请安装: pip install pyinstaller")
        return False

def main():
    """主函数"""
    print("办公辅助系统 - 干净打包工具")
    print("=" * 40)
    
    # 清理旧文件
    clean_build()
    
    # 构建干净的exe
    if build_clean_exe():
        print("\n打包成功！")
        print("生成的exe文件特点:")
        print("• 无控制台窗口")
        print("• 无emoji编码问题")
        print("• 干净的用户体验")
        print("• 单文件部署")
    else:
        print("\n打包失败！")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
