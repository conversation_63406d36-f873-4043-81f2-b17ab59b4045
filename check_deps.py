#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的依赖检查脚本
"""

def check_all_dependencies():
    """检查所有依赖"""
    print("🔍 检查依赖安装情况...")
    print("="*50)
    
    # 检查PyInstaller
    print("\n📦 检查PyInstaller:")
    try:
        import PyInstaller
        print(f"✅ PyInstaller 已安装 (版本: {PyInstaller.__version__})")
    except ImportError:
        print("❌ PyInstaller 未安装")
        print("   安装命令: pip install pyinstaller")
    
    # 检查pywebview
    print("\n📦 检查pywebview:")
    try:
        import webview
        print(f"✅ pywebview 已安装 (版本: {webview.__version__})")
    except ImportError:
        print("❌ pywebview 未安装")
        print("   安装命令: pip install pywebview")
    
    # 检查openpyxl
    print("\n📦 检查openpyxl:")
    try:
        import openpyxl
        print(f"✅ openpyxl 已安装 (版本: {openpyxl.__version__})")
    except ImportError:
        print("❌ openpyxl 未安装")
        print("   安装命令: pip install openpyxl")
    
    # 检查requests
    print("\n📦 检查requests:")
    try:
        import requests
        print(f"✅ requests 已安装 (版本: {requests.__version__})")
    except ImportError:
        print("❌ requests 未安装")
        print("   安装命令: pip install requests")
    
    # 检查本地模块
    print("\n📦 检查本地模块:")
    local_modules = ['apis', 'cmm', 'sqlite3_util']
    
    for module in local_modules:
        try:
            __import__(module)
            print(f"✅ {module}.py 可导入")
        except ImportError as e:
            print(f"❌ {module}.py 导入失败: {e}")
    
    print("\n" + "="*50)
    print("✅ 依赖检查完成！")

if __name__ == "__main__":
    check_all_dependencies()
