<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下载功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 浏览器下载功能测试</h1>
        
        <div class="test-section">
            <h3>📄 测试1：简单文本下载</h3>
            <p>测试基本的文本文件下载功能</p>
            <button onclick="testSimpleDownload()">下载简单文本</button>
            <div id="log1" class="log"></div>
        </div>

        <div class="test-section">
            <h3>📊 测试2：CSV文件下载</h3>
            <p>测试CSV格式文件下载（模拟用户数据）</p>
            <button onclick="testCSVDownload()">下载CSV文件</button>
            <div id="log2" class="log"></div>
        </div>

        <div class="test-section">
            <h3>🔧 测试3：带BOM的CSV下载</h3>
            <p>测试带BOM的CSV文件下载（支持中文）</p>
            <button onclick="testBOMCSVDownload()">下载带BOM的CSV</button>
            <div id="log3" class="log"></div>
        </div>

        <div class="test-section">
            <h3>🌐 浏览器信息</h3>
            <div id="browserInfo" class="log"></div>
        </div>

        <div class="test-section">
            <h3>💡 说明</h3>
            <ul>
                <li>如果点击按钮后没有下载，请检查浏览器的下载设置</li>
                <li>某些浏览器可能会阻止自动下载，需要用户确认</li>
                <li>检查浏览器的下载文件夹</li>
                <li>查看浏览器控制台是否有错误信息</li>
            </ul>
        </div>
    </div>

    <script>
        // 日志函数
        function log(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            container.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            container.scrollTop = container.scrollHeight;
        }

        // 测试1：简单文本下载
        function testSimpleDownload() {
            log('log1', '🚀 开始测试简单文本下载...');
            
            try {
                const content = '这是一个测试文件\n测试时间：' + new Date().toLocaleString();
                const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
                
                log('log1', `📦 创建Blob: ${blob.size} 字节`);
                
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.href = url;
                link.download = 'test_simple.txt';
                link.style.display = 'none';
                
                document.body.appendChild(link);
                log('log1', '🔗 创建下载链接');
                
                link.click();
                log('log1', '✅ 触发下载成功');
                
                setTimeout(() => {
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);
                    log('log1', '🧹 清理完成');
                }, 100);
                
            } catch (error) {
                log('log1', `❌ 下载失败: ${error.message}`, 'error');
            }
        }

        // 测试2：CSV文件下载
        function testCSVDownload() {
            log('log2', '🚀 开始测试CSV文件下载...');
            
            try {
                const headers = ['ID', '用户昵称', '抖音ID', '蝉妈妈ID', '简介', '联系方式', '来源文件', '创建时间'];
                const rows = [
                    ['1', '测试用户1', 'user1', 'cmm1', '商务合作：contact1', 'contact1', 'test1.xlsx', '2024-01-01 12:00:00'],
                    ['2', '测试用户2', 'user2', 'cmm2', '微信：wx123', 'wx123', 'test2.xlsx', '2024-01-01 12:01:00'],
                    ['3', '测试用户3', 'user3', 'cmm3', '普通简介，没有联系方式', '', 'test3.xlsx', '2024-01-01 12:02:00']
                ];
                
                let csvContent = headers.join(',') + '\n';
                rows.forEach(row => {
                    const processedRow = row.map(field => {
                        const str = String(field || '');
                        if (str.includes(',') || str.includes('"') || str.includes('\n')) {
                            return '"' + str.replace(/"/g, '""') + '"';
                        }
                        return str;
                    });
                    csvContent += processedRow.join(',') + '\n';
                });
                
                log('log2', `📊 生成CSV内容: ${csvContent.length} 字符`);
                
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
                log('log2', `📦 创建Blob: ${blob.size} 字节`);
                
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.href = url;
                
                const timestamp = new Date().toISOString().slice(0, 16).replace(/[-:T]/g, '');
                link.download = `用户数据_${timestamp}.csv`;
                link.style.display = 'none';
                
                document.body.appendChild(link);
                log('log2', `🔗 创建下载链接: ${link.download}`);
                
                link.click();
                log('log2', '✅ 触发下载成功');
                
                setTimeout(() => {
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);
                    log('log2', '🧹 清理完成');
                }, 100);
                
            } catch (error) {
                log('log2', `❌ 下载失败: ${error.message}`, 'error');
            }
        }

        // 测试3：带BOM的CSV下载
        function testBOMCSVDownload() {
            log('log3', '🚀 开始测试带BOM的CSV下载...');
            
            try {
                const headers = ['ID', '用户昵称', '抖音ID', '蝉妈妈ID', '简介', '联系方式', '来源文件', '创建时间'];
                const rows = [
                    ['1', '测试用户1', 'user1', 'cmm1', '商务合作：contact1', 'contact1', 'test1.xlsx', '2024-01-01 12:00:00'],
                    ['2', '测试用户2', 'user2', 'cmm2', '微信：wx123', 'wx123', 'test2.xlsx', '2024-01-01 12:01:00']
                ];
                
                let csvContent = headers.join(',') + '\n';
                rows.forEach(row => {
                    csvContent += row.join(',') + '\n';
                });
                
                // 添加BOM
                const BOM = '\uFEFF';
                csvContent = BOM + csvContent;
                
                log('log3', `📊 生成带BOM的CSV: ${csvContent.length} 字符`);
                
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
                log('log3', `📦 创建Blob: ${blob.size} 字节`);
                
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.href = url;
                
                const timestamp = new Date().toISOString().slice(0, 16).replace(/[-:T]/g, '');
                link.download = `用户数据_BOM_${timestamp}.csv`;
                link.style.display = 'none';
                
                document.body.appendChild(link);
                log('log3', `🔗 创建下载链接: ${link.download}`);
                
                link.click();
                log('log3', '✅ 触发下载成功');
                
                setTimeout(() => {
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);
                    log('log3', '🧹 清理完成');
                }, 100);
                
            } catch (error) {
                log('log3', `❌ 下载失败: ${error.message}`, 'error');
            }
        }

        // 显示浏览器信息
        function showBrowserInfo() {
            const info = document.getElementById('browserInfo');
            info.innerHTML = `
                <div><strong>用户代理:</strong> ${navigator.userAgent}</div>
                <div><strong>平台:</strong> ${navigator.platform}</div>
                <div><strong>语言:</strong> ${navigator.language}</div>
                <div><strong>Cookie启用:</strong> ${navigator.cookieEnabled}</div>
                <div><strong>在线状态:</strong> ${navigator.onLine}</div>
                <div><strong>屏幕分辨率:</strong> ${screen.width}x${screen.height}</div>
                <div><strong>当前时间:</strong> ${new Date().toLocaleString()}</div>
            `;
        }

        // 页面加载时显示浏览器信息
        window.onload = function() {
            showBrowserInfo();
        };
    </script>
</body>
</html>
