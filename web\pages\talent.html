<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>达人数据提取</title>
    
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Element Plus Icons -->
    <script src="//unpkg.com/@element-plus/icons-vue"></script>
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    
    <style>
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
            background: #f8fafc;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        }

        /* 子界面元素动画 */
        @keyframes smoothSlideIn {
            0% {
                transform: translateY(30px) scale(0.95);
                opacity: 0;
            }
            100% {
                transform: translateY(0) scale(1);
                opacity: 1;
            }
        }

        .animate-slide-in {
            animation: smoothSlideIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .animate-delay-1 { animation-delay: 0.15s; }
        .animate-delay-2 { animation-delay: 0.3s; }
        .animate-delay-3 { animation-delay: 0.45s; }
        .animate-delay-4 { animation-delay: 0.6s; }

        /* 上传区域样式 */
        .upload-area {
            border: 2px dashed #d1d5db;
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            background: white;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #3b82f6;
            background: #f8fafc;
        }

        .upload-area.dragover {
            border-color: #3b82f6;
            background: #eff6ff;
        }

        /* 搜索卡片样式 */
        .search-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        /* 表格卡片样式 */
        .table-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div id="app" style="padding: 24px;">
        <!-- 第一行：文件上传区域 -->
        <div class="animate-slide-in" style="margin-bottom: 24px;">
            <el-card shadow="hover" style="border-radius: 12px;">
                <template #header>
                    <div style="display: flex; align-items: center;">
                        <el-icon size="20" style="margin-right: 8px; color: #3b82f6;"><Upload /></el-icon>
                        <span style="font-weight: 600; font-size: 16px;">Excel文件上传分析</span>
                    </div>
                </template>
                <el-upload
                    class="upload-demo"
                    drag
                    :action="uploadUrl"
                    :on-success="handleUploadSuccess"
                    :on-error="handleUploadError"
                    :before-upload="beforeUpload"
                    accept=".xlsx,.xls"
                    :show-file-list="true"
                    :limit="1"
                >
                    <div class="upload-area">
                        <el-icon size="48" style="color: #3b82f6; margin-bottom: 16px;"><DocumentAdd /></el-icon>
                        <div style="font-size: 16px; color: #374151; margin-bottom: 8px;">将Excel文件拖拽到此处，或点击上传</div>
                        <div style="font-size: 14px; color: #6b7280;">支持 .xlsx, .xls 格式文件</div>
                    </div>
                </el-upload>
            </el-card>
        </div>

        <!-- 第二行：搜索区域 -->
        <div class="animate-slide-in animate-delay-1" style="margin-bottom: 24px;">
            <el-card shadow="hover" class="search-card">
                <template #header>
                    <div style="display: flex; align-items: center;">
                        <el-icon size="20" style="margin-right: 8px; color: #10b981;"><Search /></el-icon>
                        <span style="font-weight: 600; font-size: 16px;">数据搜索</span>
                    </div>
                </template>
                <el-form :model="searchForm" label-width="80px">
                    <el-row :gutter="24">
                        <el-col :span="6">
                            <el-form-item label="文件名">
                                <el-input
                                    v-model="searchForm.fileName"
                                    placeholder="请输入文件名"
                                    clearable
                                >
                                    <template #prefix>
                                        <el-icon><Document /></el-icon>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="简介信息">
                                <el-input
                                    v-model="searchForm.description"
                                    placeholder="请输入简介信息"
                                    clearable
                                >
                                    <template #prefix>
                                        <el-icon><InfoFilled /></el-icon>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="手机号">
                                <el-input
                                    v-model="searchForm.phone"
                                    placeholder="请输入手机号"
                                    clearable
                                >
                                    <template #prefix>
                                        <el-icon><Phone /></el-icon>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="微信号">
                                <el-input
                                    v-model="searchForm.wechat"
                                    placeholder="请输入微信号"
                                    clearable
                                >
                                    <template #prefix>
                                        <el-icon><ChatDotRound /></el-icon>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24" style="text-align: right;">
                            <el-button @click="resetSearch">
                                <el-icon style="margin-right: 4px;"><Refresh /></el-icon>
                                重置
                            </el-button>
                            <el-button type="primary" @click="handleSearch">
                                <el-icon style="margin-right: 4px;"><Search /></el-icon>
                                搜索
                            </el-button>
                        </el-col>
                    </el-row>
                </el-form>
            </el-card>
        </div>

        <!-- 第三行：数据表格 -->
        <div class="animate-slide-in animate-delay-2" style="margin-bottom: 24px;">
            <el-card shadow="hover" class="table-card">
                <template #header>
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div style="display: flex; align-items: center;">
                            <el-icon size="20" style="margin-right: 8px; color: #f59e0b;"><Grid /></el-icon>
                            <span style="font-weight: 600; font-size: 16px;">联系人数据列表</span>
                            <el-tag type="info" size="small" style="margin-left: 12px;">共 {{ total }} 条数据</el-tag>
                        </div>
                        <div>
                            <el-button size="small" @click="exportData">
                                <el-icon style="margin-right: 4px;"><Download /></el-icon>
                                导出数据
                            </el-button>
                        </div>
                    </div>
                </template>
                <el-table
                    :data="tableData"
                    style="width: 100%"
                    v-loading="tableLoading"
                    element-loading-text="数据加载中..."
                    stripe
                    border
                >
                    <el-table-column prop="id" label="ID" width="80" align="center"></el-table-column>
                    <el-table-column prop="originalFileName" label="原始文件名" min-width="200">
                        <template #default="scope">
                            <div style="display: flex; align-items: center;">
                                <el-icon style="margin-right: 8px; color: #3b82f6;"><Document /></el-icon>
                                {{ scope.row.originalFileName }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="description" label="简介信息" min-width="250">
                        <template #default="scope">
                            <el-tooltip :content="scope.row.description" placement="top" :disabled="!scope.row.description || scope.row.description.length < 30">
                                <span>{{ scope.row.description || '-' }}</span>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                    <el-table-column prop="phone" label="手机号" width="140">
                        <template #default="scope">
                            <div v-if="scope.row.phone" style="display: flex; align-items: center;">
                                <el-icon style="margin-right: 4px; color: #10b981;"><Phone /></el-icon>
                                {{ scope.row.phone }}
                            </div>
                            <span v-else style="color: #9ca3af;">-</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="wechat" label="微信号" width="140">
                        <template #default="scope">
                            <div v-if="scope.row.wechat" style="display: flex; align-items: center;">
                                <el-icon style="margin-right: 4px; color: #10b981;"><ChatDotRound /></el-icon>
                                {{ scope.row.wechat }}
                            </div>
                            <span v-else style="color: #9ca3af;">-</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="120" align="center">
                        <template #default="scope">
                            <el-button type="primary" size="small" text @click="viewDetail(scope.row)">
                                <el-icon style="margin-right: 4px;"><View /></el-icon>
                                查看
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>

        <!-- 第四行：分页 -->
        <div class="animate-slide-in animate-delay-3" style="text-align: center;">
            <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[5, 10, 20]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                background
            />
        </div>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>

    <script>
        const { createApp, ref, reactive } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        const app = createApp({
            setup() {
                // 搜索表单
                const searchForm = reactive({
                    fileName: '',
                    description: '',
                    phone: '',
                    wechat: ''
                });

                // 表格数据
                const tableData = ref([
                    {
                        id: 1,
                        originalFileName: 'contacts_2024_01.xlsx',
                        description: '2024年1月份客户联系人信息表',
                        phone: '13800138001',
                        wechat: 'user001'
                    },
                    {
                        id: 2,
                        originalFileName: 'leads_data.xlsx',
                        description: '潜在客户信息收集表，包含详细的联系方式和需求描述',
                        phone: '13800138002',
                        wechat: 'user002'
                    },
                    {
                        id: 3,
                        originalFileName: 'customer_info.xlsx',
                        description: '客户基础信息表',
                        phone: '',
                        wechat: 'user003'
                    },
                    {
                        id: 4,
                        originalFileName: 'contact_list.xlsx',
                        description: '',
                        phone: '***********',
                        wechat: ''
                    },
                    {
                        id: 5,
                        originalFileName: 'business_contacts.xlsx',
                        description: '商务联系人信息汇总表',
                        phone: '***********',
                        wechat: 'user005'
                    }
                ]);

                // 分页数据
                const currentPage = ref(1);
                const pageSize = ref(5);
                const total = ref(5);
                const tableLoading = ref(false);

                // 上传相关
                const uploadUrl = ref('');

                // 搜索功能
                const handleSearch = () => {
                    tableLoading.value = true;
                    setTimeout(() => {
                        tableLoading.value = false;
                        ElMessage.success('搜索完成');
                    }, 1000);
                };

                const resetSearch = () => {
                    Object.keys(searchForm).forEach(key => {
                        searchForm[key] = '';
                    });
                    ElMessage.info('搜索条件已重置');
                };

                // 分页功能
                const handleSizeChange = (val) => {
                    pageSize.value = val;
                    currentPage.value = 1;
                };

                const handleCurrentChange = (val) => {
                    currentPage.value = val;
                };

                // 上传功能
                const handleUploadSuccess = (response, file) => {
                    ElMessage.success('文件上传成功');
                };

                const handleUploadError = (error, file) => {
                    ElMessage.error('文件上传失败');
                };

                const beforeUpload = (file) => {
                    const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                                   file.type === 'application/vnd.ms-excel';
                    const isLt10M = file.size / 1024 / 1024 < 10;

                    if (!isExcel) {
                        ElMessage.error('只能上传Excel文件!');
                        return false;
                    }
                    if (!isLt10M) {
                        ElMessage.error('文件大小不能超过10MB!');
                        return false;
                    }
                    return true;
                };

                // 其他功能
                const exportData = () => {
                    ElMessage.success('数据导出功能开发中...');
                };

                const viewDetail = (row) => {
                    ElMessage.info(`查看详情: ${row.originalFileName}`);
                };

                return {
                    searchForm,
                    tableData,
                    currentPage,
                    pageSize,
                    total,
                    tableLoading,
                    uploadUrl,
                    handleSearch,
                    resetSearch,
                    handleSizeChange,
                    handleCurrentChange,
                    handleUploadSuccess,
                    handleUploadError,
                    beforeUpload,
                    exportData,
                    viewDetail
                };
            }
        });

        // 注册所有图标
        Object.keys(ElementPlusIconsVue).forEach(key => {
            app.component(key, ElementPlusIconsVue[key]);
        });

        app.use(ElementPlus).mount('#app');
    </script>
</body>
</html>
