<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>达人数据提取</title>

    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Element Plus Icons -->
    <script src="//unpkg.com/@element-plus/icons-vue"></script>
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <style>
        html,
        body {
            margin: 0;
            padding: 0;
            height: 100%;
            background: #f8fafc;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        }

        /* 子界面元素动画 */
        @keyframes smoothSlideIn {
            0% {
                transform: translateY(30px) scale(0.95);
                opacity: 0;
            }

            100% {
                transform: translateY(0) scale(1);
                opacity: 1;
            }
        }

        .animate-slide-in {
            animation: smoothSlideIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .animate-delay-1 {
            animation-delay: 0.15s;
        }

        .animate-delay-2 {
            animation-delay: 0.3s;
        }

        .animate-delay-3 {
            animation-delay: 0.45s;
        }

        .animate-delay-4 {
            animation-delay: 0.6s;
        }

        /* 上传区域样式 */
        .upload-area {
            border: 1px dashed #d1d5db;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            background: #fafbfc;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #3b82f6;
            background: #f0f9ff;
        }

        .upload-area.dragover {
            border-color: #3b82f6;
            background: #eff6ff;
        }

        /* 搜索卡片样式 */
        .search-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        /* 表格卡片样式 */
        .table-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
        }
    </style>
</head>

<body>
    <div id="app" style="padding: 24px;">
        <!-- 文件拖拽上传区域 -->
        <div class="animate-slide-in" style="margin-bottom: 16px;">
            <el-upload ref="uploadRef" class="upload-demo" drag :auto-upload="false" :on-change="handleFileChange"
                :show-file-list="false" accept=".xlsx,.xls" :limit="1">
                <div class="upload-area">
                    <el-icon size="48" style="color: #3b82f6; margin-bottom: 16px;">
                        <Upload />
                    </el-icon>
                    <div style="font-size: 16px; color: #374151; margin-bottom: 8px;">
                        {{ selectedFileName || '将Excel文件拖拽到此处，或点击选择文件' }}
                    </div>
                    <div style="font-size: 14px; color: #6b7280;">支持 .xlsx, .xls 格式文件</div>
                </div>
            </el-upload>
        </div>

        <!-- 工作日志 -->
        <div class="animate-slide-in animate-delay-1" style="margin-bottom: 24px;"
            v-if="workLogs.length > 0 || tableLoading">
            <el-card shadow="hover" style="border-radius: 12px;">
                <template #header>
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div style="display: flex; align-items: center;">
                            <el-icon size="18" style="margin-right: 8px; color: #10b981;">
                                <Document />
                            </el-icon>
                            <span style="font-weight: 600; font-size: 16px;">工作日志</span>
                            <el-tag type="info" size="small" style="margin-left: 12px;">共 {{ workLogs.length }}
                                条记录</el-tag>
                        </div>
                        <el-button size="small" @click="clearLogs" v-if="workLogs.length > 0">
                            <el-icon style="margin-right: 4px;">
                                <Delete />
                            </el-icon>
                            清空日志
                        </el-button>
                    </div>
                </template>

                <!-- 正在处理状态 -->
                <div v-if="isProcessingFile" style="padding: 20px; text-align: center;">
                    <el-icon size="24" style="color: #3b82f6; margin-bottom: 12px;" class="is-loading">
                        <Loading />
                    </el-icon>
                    <div style="font-size: 16px; color: #374151; margin-bottom: 8px;">正在处理Excel文件...</div>
                    <div style="font-size: 14px; color: #6b7280;">{{ selectedFileName }}</div>
                </div>

                <!-- 日志列表 -->
                <div v-if="workLogs.length > 0" style="max-height: 300px; overflow-y: auto;">
                    <div v-for="log in workLogs" :key="log.id"
                        style="border-bottom: 1px solid #f0f0f0; padding: 16px 0;"
                        :style="{ 'border-bottom': workLogs.indexOf(log) === workLogs.length - 1 ? 'none' : '1px solid #f0f0f0' }">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <div style="flex: 1;">
                                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                    <el-icon size="16" style="margin-right: 8px;"
                                        :style="{ color: log.status === 'success' ? '#10b981' : '#ef4444' }">
                                        <CircleCheck v-if="log.status === 'success'" />
                                        <CircleClose v-else />
                                    </el-icon>
                                    <span style="font-weight: 600; font-size: 14px;">{{ log.filename }}</span>
                                    <el-tag :type="log.status === 'success' ? 'success' : 'danger'" size="small"
                                        style="margin-left: 8px;">
                                        {{ log.status === 'success' ? '处理成功' : '处理失败' }}
                                    </el-tag>
                                    <span style="font-size: 12px; color: #9ca3af; margin-left: 8px;">{{ log.time
                                        }}</span>
                                </div>

                                <div v-if="log.status === 'success' && log.details"
                                    style="font-size: 13px; color: #6b7280;">
                                    工作表: {{ log.details.currentSheet }} |
                                    数据行数: {{ log.details.totalRows }} |
                                    列数: {{ log.details.columns }} |
                                    超链接: {{ log.details.hyperlinksCount }}个 |
                                    提取ID: {{ log.details.sqliteReadyCount }}个 |
                                    <span :style="{ color: log.details.dbInsertSuccess ? '#10b981' : '#ef4444' }">
                                        数据库: {{ log.details.dbInsertSuccess ? '插入成功' : '插入失败' }}
                                    </span>
                                </div>

                                <div v-if="log.status === 'error'" style="font-size: 13px; color: #ef4444;">
                                    错误: {{ log.error }}
                                </div>
                            </div>

                            <div style="margin-left: 16px;">
                                <el-button v-if="log.status === 'success'" type="primary" size="small" text
                                    @click="viewLogDetails(log)">
                                    详情
                                </el-button>
                            </div>
                        </div>
                    </div>
                </div>
            </el-card>
        </div>

        <!-- 数据表格 -->
        <div class="animate-slide-in animate-delay-1">
            <el-card shadow="hover" class="table-card">
                <template #header>
                    <div
                        style="display: flex; align-items: center; justify-content: space-between; flex-wrap: wrap; gap: 16px;">
                        <div style="display: flex; align-items: center;">
                            <el-icon size="18" style="margin-right: 8px; color: #3b82f6;">
                                <Grid />
                            </el-icon>
                            <span style="font-weight: 600; font-size: 16px;">联系人数据列表</span>
                            <el-tag type="info" size="small" style="margin-left: 12px;">共 {{ total }} 条数据</el-tag>
                        </div>

                        <!-- 搜索区域集成到表格头部 -->
                        <div style="display: flex; align-items: center; gap: 12px; flex-wrap: wrap;">
                            <el-input v-model="searchForm.fileName" placeholder="文件名" clearable size="small"
                                style="width: 120px;">
                                <template #prefix>
                                    <el-icon>
                                        <Document />
                                    </el-icon>
                                </template>
                            </el-input>
                            <el-input v-model="searchForm.description" placeholder="简介信息" clearable size="small"
                                style="width: 120px;">
                                <template #prefix>
                                    <el-icon>
                                        <InfoFilled />
                                    </el-icon>
                                </template>
                            </el-input>
                            <el-input v-model="searchForm.phone" placeholder="手机号" clearable size="small"
                                style="width: 120px;">
                                <template #prefix>
                                    <el-icon>
                                        <Phone />
                                    </el-icon>
                                </template>
                            </el-input>
                            <el-input v-model="searchForm.wechat" placeholder="微信号" clearable size="small"
                                style="width: 120px;">
                                <template #prefix>
                                    <el-icon>
                                        <ChatDotRound />
                                    </el-icon>
                                </template>
                            </el-input>
                            <el-button size="small" @click="handleSearch" type="primary">
                                <el-icon>
                                    <Search />
                                </el-icon>
                                搜索
                            </el-button>
                            <el-button size="small" @click="resetSearch">
                                <el-icon>
                                    <Refresh />
                                </el-icon>
                                重置
                            </el-button>
                            <el-button size="small" @click="refreshData" type="success">
                                <el-icon>
                                    <RefreshRight />
                                </el-icon>
                                刷新
                            </el-button>
                            <el-button size="small" @click="exportData" type="primary">
                                <el-icon>
                                    <Download />
                                </el-icon>
                                导出
                            </el-button>
                        </div>
                    </div>
                </template>
                <el-table :data="tableData" style="width: 100%;" stripe border :cell-style="{
                        'white-space': 'nowrap',
                        'overflow': 'hidden',
                        'text-overflow': 'ellipsis',
                        'padding': '8px 12px'
                    }" :header-cell-style="{
                        'white-space': 'nowrap',
                        'overflow': 'hidden',
                        'text-overflow': 'ellipsis'
                    }">
                    <el-table-column prop="id" label="ID" width="80" align="center"
                        show-overflow-tooltip></el-table-column>
                    <el-table-column prop="username" label="用户昵称" min-width="120"
                        show-overflow-tooltip></el-table-column>
                    <el-table-column prop="unique_id" label="抖音ID" min-width="120"
                        show-overflow-tooltip></el-table-column>
                    <el-table-column prop="cmm_id" label="蝉妈妈ID" min-width="180"
                        show-overflow-tooltip></el-table-column>
                    <el-table-column prop="intro" label="简介" min-width="200" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="file_name" label="来源文件" min-width="120"
                        show-overflow-tooltip></el-table-column>
                    <el-table-column prop="create_time" label="创建时间" width="160"
                        show-overflow-tooltip></el-table-column>
                    <el-table-column label="操作" width="120" align="center">
                        <template #default="scope">
                            <el-button type="primary" size="small" text @click="viewDetail(scope.row)">
                                <el-icon style="margin-right: 4px;">
                                    <View />
                                </el-icon>
                                查看
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                 <!-- 分页 -->
            <div style="text-align: center; margin-top: 16px;">
                <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[5, 10, 20]"
                    :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
                    @current-change="handleCurrentChange" background small />
            </div>
            </el-card>
           
        </div>
    </div>
    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>

    <script>
        const { createApp, ref, reactive, onMounted } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        const app = createApp({
            setup() {
                // 搜索表单
                const searchForm = reactive({
                    fileName: '',
                    description: '',
                    phone: '',
                    wechat: ''
                });

                // 表格数据
                const tableData = ref([]);

                // 分页数据
                const currentPage = ref(1);
                const pageSize = ref(5);  // 前端显示：默认5条
                const total = ref(0);
                const tableLoading = ref(false);

                // 数据库分页缓存策略
                const dbPageSize = 200; // 数据库每页200条
                const cachedData = ref([]); // 缓存的数据库数据
                const currentDbPage = ref(1); // 当前数据库页码
                const lastSearchParams = ref(null); // 上次搜索参数

                // 文件选择相关
                const selectedFileName = ref('');
                const workLogs = ref([]);
                const uploadRef = ref(null);
                const isProcessingFile = ref(false);

                // 获取pywebview API
                const getPywebviewApi = () => {
                    if (window.pywebview && window.pywebview.api) {
                        return window.pywebview.api;
                    } else if (window.parent && window.parent.pywebview && window.parent.pywebview.api) {
                        return window.parent.pywebview.api;
                    } else if (window.top && window.top.pywebview && window.top.pywebview.api) {
                        return window.top.pywebview.api;
                    }
                    return null;
                };

                // 加载用户数据（初始化时使用）
                const loadData = async (resetPage = false) => {
                    await loadDataFromDB(resetPage);
                };

                // 数据库分页加载策略
                const loadDataFromDB = async (resetPage = false) => {
                    if (resetPage) {
                        currentPage.value = 1;
                        cachedData.value = [];
                        currentDbPage.value = 1;
                    }

                    try {
                        // 准备搜索参数
                        const searchParams = {};
                        if (searchForm.fileName && searchForm.fileName.trim()) {
                            searchParams.fileName = searchForm.fileName.trim();
                        }
                        if (searchForm.description && searchForm.description.trim()) {
                            searchParams.description = searchForm.description.trim();
                        }
                        if (searchForm.phone && searchForm.phone.trim()) {
                            searchParams.phone = searchForm.phone.trim();
                        }
                        if (searchForm.wechat && searchForm.wechat.trim()) {
                            searchParams.wechat = searchForm.wechat.trim();
                        }

                        // 检查搜索条件是否变化
                        const searchParamsStr = JSON.stringify(searchParams);
                        const lastSearchParamsStr = JSON.stringify(lastSearchParams.value);
                        if (searchParamsStr !== lastSearchParamsStr) {
                            // 搜索条件变化，清空缓存
                            cachedData.value = [];
                            currentDbPage.value = 1;
                            lastSearchParams.value = searchParams;
                        }

                        // 计算前端需要显示的数据范围
                        const startIndex = (currentPage.value - 1) * pageSize.value;
                        const endIndex = startIndex + pageSize.value;

                        // 检查缓存是否足够
                        const needMoreData = endIndex > cachedData.value.length && cachedData.value.length < total.value;

                        if (needMoreData || cachedData.value.length === 0) {
                            // 需要从数据库获取更多数据
                            const pywebviewApi = getPywebviewApi();

                            if (pywebviewApi) {
                                console.log(`🔄 从数据库获取第${currentDbPage.value}页数据（每页${dbPageSize}条）`);

                                // 从数据库获取200条数据
                                const result = await pywebviewApi.get_users_data(
                                    currentDbPage.value,
                                    dbPageSize, // 200条
                                    Object.keys(searchParams).length > 0 ? searchParams : null
                                );

                                if (result.success) {
                                    if (currentDbPage.value === 1) {
                                        // 第一页，直接设置
                                        cachedData.value = result.data || [];
                                        total.value = result.total || 0;
                                    } else {
                                        // 后续页，追加到缓存
                                        cachedData.value = [...cachedData.value, ...(result.data || [])];
                                    }
                                    currentDbPage.value++;

                                    console.log(`✅ 缓存数据: ${cachedData.value.length}条，总计: ${total.value}条`);
                                } else {
                                    ElMessage.error(`数据加载失败: ${result.message}`);
                                    if (currentDbPage.value === 1) {
                                        tableData.value = [];
                                        total.value = 0;
                                        return;
                                    }
                                }
                            } else {
                                ElMessage.error('系统API不可用');
                                tableData.value = [];
                                total.value = 0;
                                return;
                            }
                        }

                        // 从缓存中提取当前页数据
                        tableData.value = cachedData.value.slice(startIndex, endIndex);
                        console.log(`📄 显示第${currentPage.value}页: ${startIndex}-${endIndex-1}，实际显示${tableData.value.length}条`);

                    } catch (error) {
                        ElMessage.error(`数据加载异常: ${error.message}`);
                        tableData.value = [];
                        total.value = 0;
                    }
                };

                // 刷新数据
                const refreshData = () => {
                    loadDataFromDB(false);
                };

                // 搜索功能
                const handleSearch = () => {
                    loadDataFromDB(true); // 重置到第一页
                };

                const resetSearch = () => {
                    Object.keys(searchForm).forEach(key => {
                        searchForm[key] = '';
                    });
                    loadDataFromDB(true); // 重置到第一页
                    ElMessage.info('搜索条件已重置');
                };

                // 分页功能
                const handleSizeChange = (val) => {
                    pageSize.value = val;
                    currentPage.value = 1;
                    loadDataFromDB(false); // 从缓存获取
                };

                const handleCurrentChange = (val) => {
                    currentPage.value = val;

                    // 检查是否需要预加载（当前显示接近缓存80%时）
                    const startIndex = (currentPage.value - 1) * pageSize.value;
                    const endIndex = startIndex + pageSize.value;
                    const cacheUsagePercent = endIndex / cachedData.value.length;

                    if (cacheUsagePercent >= 0.8 && cachedData.value.length < total.value) {
                        console.log(`🚀 预加载触发: 使用率${(cacheUsagePercent*100).toFixed(1)}%，预加载下一页数据`);
                        // 异步预加载，不影响当前页面显示
                        setTimeout(() => {
                            loadDataFromDB(false);
                        }, 100);
                    } else {
                        // 直接从缓存获取
                        loadDataFromDB(false);
                    }
                };

                // 文件选择处理
                const handleFileChange = (file) => {
                    console.log('=== 文件选择开始 ===');
                    console.log('file对象:', file);
                    console.log('file.raw:', file.raw);

                    if (!file || !file.raw) {
                        console.error('文件对象无效');
                        ElMessage.error('文件选择失败');
                        return;
                    }

                    console.log('文件信息:');
                    console.log('- 名称:', file.name);
                    console.log('- 大小:', file.size);
                    console.log('- 类型:', file.type);

                    selectedFileName.value = file.name;

                    // 读取文件内容
                    const reader = new FileReader();

                    reader.onerror = (error) => {
                        console.error('文件读取错误:', error);
                        ElMessage.error('文件读取失败');
                        isProcessingFile.value = false;
                    };

                    reader.onload = async (e) => {
                        try {
                            isProcessingFile.value = true;
                            console.log('=== 文件读取完成 ===');

                            // 获取文件内容（ArrayBuffer格式）
                            const fileContent = e.target.result;
                            console.log('ArrayBuffer大小:', fileContent.byteLength);

                            // 转换为Uint8Array
                            const uint8Array = new Uint8Array(fileContent);
                            console.log('Uint8Array长度:', uint8Array.length);

                            // 转换为普通数组
                            const contentArray = Array.from(uint8Array);
                            console.log('内容数组长度:', contentArray.length);

                            // 根据文件扩展名设置正确的MIME类型
                            let fileType = file.type;
                            if (!fileType || fileType === 'undefined') {
                                const fileName = file.name.toLowerCase();
                                if (fileName.endsWith('.xlsx')) {
                                    fileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                                } else if (fileName.endsWith('.xls')) {
                                    fileType = 'application/vnd.ms-excel';
                                } else {
                                    fileType = 'application/octet-stream'; // 默认二进制类型
                                }
                                console.log('自动设置文件类型:', fileType);
                            }

                            const fileData = {
                                name: file.name,
                                size: file.size,
                                type: fileType,
                                content: contentArray
                            };

                            console.log('=== 准备发送到Python ===');
                            console.log('发送的数据:', {
                                name: fileData.name,
                                size: fileData.size,
                                type: fileData.type,
                                contentLength: fileData.content.length
                            });

                            // 检查pywebview API是否可用（iframe中需要通过parent访问）
                            let pywebviewApi = null;

                            if (window.pywebview && window.pywebview.api) {
                                pywebviewApi = window.pywebview.api;
                                console.log('使用当前窗口的pywebview API');
                            } else if (window.parent && window.parent.pywebview && window.parent.pywebview.api) {
                                pywebviewApi = window.parent.pywebview.api;
                                console.log('使用父窗口的pywebview API');
                            } else if (window.top && window.top.pywebview && window.top.pywebview.api) {
                                pywebviewApi = window.top.pywebview.api;
                                console.log('使用顶层窗口的pywebview API');
                            } else {
                                throw new Error('pywebview API不可用，请检查是否在pywebview环境中运行');
                            }

                            // 调用Python处理
                            const result = await pywebviewApi.process_file(fileData);

                            console.log('=== Python处理结果 ===');
                            console.log('结果:', result);

                            if (result && result.success) {
                                ElMessage.success(`Excel文件读取成功！`);

                                // 添加到工作日志
                                if (result.data) {
                                    const logEntry = {
                                        id: Date.now(),
                                        time: new Date().toLocaleString(),
                                        filename: result.data.filename,
                                        status: 'success',
                                        details: {
                                            sheets: result.data.sheet_names.length,
                                            currentSheet: result.data.current_sheet,
                                            rows: result.data.max_row,
                                            columns: result.data.max_column,
                                            headers: result.data.headers,
                                            totalRows: result.data.total_rows,
                                            hyperlinksCount: result.data.hyperlinks_count,
                                            hyperlinks: result.data.hyperlinks,
                                            extractedIds: result.data.extracted_ids || [],
                                            sqliteReadyCount: result.data.sqlite_ready_count || 0,
                                            dbInsertSuccess: result.data.db_insert_success || false,
                                            dbInsertMessage: result.data.db_insert_message || '未执行插入'
                                        }
                                    };
                                    workLogs.value.unshift(logEntry);

                                    console.log('=== Excel文件信息 ===');
                                    console.log('文件名:', result.data.filename);
                                    console.log('工作表数量:', result.data.sheet_names.length);
                                    console.log('工作表名称:', result.data.sheet_names);
                                    console.log('当前工作表:', result.data.current_sheet);
                                    console.log('最大行数:', result.data.max_row);
                                    console.log('最大列数:', result.data.max_column);
                                    console.log('实际数据行数:', result.data.total_rows);
                                    console.log('表头:', result.data.headers);
                                    console.log('示例数据:', result.data.sample_data);
                                    console.log('超链接数量:', result.data.hyperlinks_count);
                                    console.log('超链接详情:', result.data.hyperlinks);

                                    // 特别打印超链接信息
                                    if (result.data.hyperlinks && result.data.hyperlinks.length > 0) {
                                        console.log('\n=== 发现的超链接 ===');
                                        result.data.hyperlinks.forEach((link, index) => {
                                            console.log(`${index + 1}. 行${link.row} ${link.column_name}: ${link.cell_value} -> ${link.hyperlink}`);
                                        });
                                    }

                                    // 打印提取的ID和SQLite数据
                                    if (result.data.extracted_ids) {
                                        console.log('\n=== 提取的ID ===');
                                        console.log('ID数量:', result.data.extracted_ids.length);
                                        console.log('ID列表:', result.data.extracted_ids);
                                        console.log('SQLite数据条数:', result.data.sqlite_ready_count);
                                        console.log('SQLite数据:', result.data.sqlite_data);

                                        // 打印数据库插入结果
                                        console.log('\n=== 数据库插入结果 ===');
                                        console.log('插入成功:', result.data.db_insert_success);
                                        console.log('插入消息:', result.data.db_insert_message);

                                        // 显示插入结果消息
                                        if (result.data.db_insert_success) {
                                            ElMessage.success(result.data.db_insert_message);
                                            // 刷新表格数据
                                            setTimeout(() => {
                                                loadData();
                                            }, 1000);
                                        } else {
                                            ElMessage.warning(result.data.db_insert_message);
                                        }
                                    }
                                }

                                selectedFileName.value = '';
                            } else {
                                const errorMsg = result ? result.message : '未知错误';
                                console.error('处理失败:', errorMsg);

                                // 添加错误日志
                                const logEntry = {
                                    id: Date.now(),
                                    time: new Date().toLocaleString(),
                                    filename: file.name,
                                    status: 'error',
                                    error: errorMsg
                                };
                                workLogs.value.unshift(logEntry);

                                ElMessage.error(`Excel文件处理失败: ${errorMsg}`);
                            }

                        } catch (error) {
                            console.error('=== 文件处理异常 ===');
                            console.error('错误对象:', error);
                            console.error('错误消息:', error.message);
                            console.error('错误堆栈:', error.stack);
                            ElMessage.error(`文件处理失败: ${error.message}`);
                        } finally {
                            isProcessingFile.value = false;
                            // Python处理完毕后立即清理文件选择，允许重新选择文件
                            selectedFileName.value = '';
                            if (uploadRef.value) {
                                uploadRef.value.clearFiles();
                            }
                        }
                    };

                    console.log('开始读取文件...');
                    reader.readAsArrayBuffer(file.raw); // 读取为二进制数据
                };

                // 工作日志相关
                const clearLogs = () => {
                    ElMessageBox.confirm('确定要清空所有工作日志吗？', '确认清空', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        workLogs.value = [];
                        ElMessage.success('工作日志已清空');
                    }).catch(() => {
                        // 用户取消
                    });
                };

                const viewLogDetails = (log) => {
                    if (log.details) {
                        let details = [
                            `文件名: ${log.filename}`,
                            `处理时间: ${log.time}`,
                            `工作表: ${log.details.currentSheet}`,
                            `数据规模: ${log.details.totalRows}行 × ${log.details.columns}列`,
                            `工作表数量: ${log.details.sheets}个`,
                            `超链接数量: ${log.details.hyperlinksCount}个`,
                            `提取ID数量: ${log.details.sqliteReadyCount}个`,
                            `数据库插入: ${log.details.dbInsertSuccess ? '成功' : '失败'} - ${log.details.dbInsertMessage}`,
                            `表头字段: ${log.details.headers.join(', ')}`
                        ];

                        // 添加超链接详情
                        if (log.details.hyperlinks && log.details.hyperlinks.length > 0) {
                            details.push('\n=== 超链接详情 ===');
                            log.details.hyperlinks.forEach((link, index) => {
                                details.push(`${index + 1}. 行${link.row} ${link.column_name}: ${link.cell_value} -> ${link.hyperlink}`);
                            });
                        }

                        ElMessageBox.alert(details.join('\n'), '处理详情', {
                            confirmButtonText: '确定'
                        });
                    }
                };

                // 导出数据功能
                const exportData = async () => {
                    if (total.value === 0) {
                        ElMessage.warning('暂无数据可导出');
                        return;
                    }

                    try {
                        console.log('=== 开始导出数据 ===');
                        ElMessage.info('正在导出数据，请稍候...');

                        const pywebviewApi = getPywebviewApi();
                        if (!pywebviewApi) {
                            ElMessage.error('系统API不可用');
                            return;
                        }

                        // 准备搜索参数（与当前搜索条件一致）
                        const searchParams = {};
                        if (searchForm.fileName && searchForm.fileName.trim()) {
                            searchParams.fileName = searchForm.fileName.trim();
                        }
                        if (searchForm.description && searchForm.description.trim()) {
                            searchParams.description = searchForm.description.trim();
                        }
                        if (searchForm.phone && searchForm.phone.trim()) {
                            searchParams.phone = searchForm.phone.trim();
                        }
                        if (searchForm.wechat && searchForm.wechat.trim()) {
                            searchParams.wechat = searchForm.wechat.trim();
                        }

                        console.log('导出搜索条件:', searchParams);
                        console.log('导出总记录数:', total.value);

                        // 获取所有符合条件的数据（使用大分页）
                        const result = await pywebviewApi.get_users_data(
                            1,
                            Math.min(total.value, 10000), // 最多1万条，避免请求过大
                            Object.keys(searchParams).length > 0 ? searchParams : null
                        );

                        console.log('导出数据结果:', result);

                        if (!result.success || !result.data) {
                            ElMessage.error('获取导出数据失败');
                            return;
                        }

                        // 准备导出数据
                        const exportHeaders = ['ID', '用户昵称', '抖音ID', '蝉妈妈ID', '简介', '来源文件', '创建时间'];
                        const exportRows = result.data.map(row => [
                            row.id || '',
                            row.username || '',
                            row.unique_id || '',
                            row.cmm_id || '',
                            row.intro || '',
                            row.file_name || '',
                            row.create_time || ''
                        ]);

                        console.log('准备导出行数:', exportRows.length);

                        // 创建CSV内容
                        let csvContent = exportHeaders.join(',') + '\n';
                        exportRows.forEach(row => {
                            // 处理包含逗号的字段，用双引号包围
                            const processedRow = row.map(field => {
                                const str = String(field || '');
                                if (str.includes(',') || str.includes('"') || str.includes('\n')) {
                                    return '"' + str.replace(/"/g, '""') + '"';
                                }
                                return str;
                            });
                            csvContent += processedRow.join(',') + '\n';
                        });

                        console.log('CSV内容长度:', csvContent.length);

                        // 添加BOM以支持中文
                        const BOM = '\uFEFF';
                        csvContent = BOM + csvContent;

                        // 创建下载链接
                        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                        const link = document.createElement('a');
                        const url = URL.createObjectURL(blob);
                        link.setAttribute('href', url);

                        // 生成文件名
                        const now = new Date();
                        const timestamp = now.getFullYear() +
                            String(now.getMonth() + 1).padStart(2, '0') +
                            String(now.getDate()).padStart(2, '0') + '_' +
                            String(now.getHours()).padStart(2, '0') +
                            String(now.getMinutes()).padStart(2, '0');

                        const fileName = `用户数据_${timestamp}.csv`;
                        link.setAttribute('download', fileName);
                        link.style.visibility = 'hidden';
                        document.body.appendChild(link);

                        console.log('开始下载文件:', fileName);
                        link.click();

                        // 清理
                        setTimeout(() => {
                            document.body.removeChild(link);
                            URL.revokeObjectURL(url);
                        }, 100);

                        console.log('=== 导出完成 ===');
                        ElMessage.success(`成功导出 ${result.data.length} 条数据到文件: ${fileName}`);

                    } catch (error) {
                        console.error('导出失败:', error);
                        ElMessage.error('导出失败: ' + error.message);
                    }
                };

                const viewDetail = (row) => {
                    ElMessage.info(`查看详情: ${row.originalFileName}`);
                };

                // 页面加载时初始化数据
                onMounted(() => {
                    loadData();
                });

                return {
                    searchForm,
                    tableData,
                    currentPage,
                    pageSize,
                    total,
                    tableLoading,
                    isProcessingFile,
                    selectedFileName,
                    workLogs,
                    uploadRef,
                    loadData,
                    refreshData,
                    handleSearch,
                    resetSearch,
                    handleSizeChange,
                    handleCurrentChange,
                    handleFileChange,
                    clearLogs,
                    viewLogDetails,
                    exportData,
                    viewDetail
                };
            }
        });

        // 注册所有图标
        Object.keys(ElementPlusIconsVue).forEach(key => {
            app.component(key, ElementPlusIconsVue[key]);
        });

        app.use(ElementPlus).mount('#app');
    </script>
</body>

</html>