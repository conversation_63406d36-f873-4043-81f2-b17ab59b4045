<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>达人数据提取</title>
    
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Element Plus Icons -->
    <script src="//unpkg.com/@element-plus/icons-vue"></script>
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    
    <style>
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
            background: var(--el-bg-color-page);
        }
    </style>
</head>
<body>
    <div id="app" style="padding: 30px;">
        <!-- 页面标题 -->
        <el-card shadow="never" style="margin-bottom: 24px;" class="animate__animated animate__fadeInUp">
            <div style="text-align: center; padding: 60px 0;">
                <el-icon size="80" color="var(--el-color-primary)"><DataAnalysis /></el-icon>
                <h1 style="margin: 24px 0 12px 0; color: var(--el-text-color-primary); font-size: 32px; font-weight: 600;">达人数据提取</h1>
                <p style="color: var(--el-text-color-regular); font-size: 18px; margin: 0;">蝉妈妈平台达人数据分析工具</p>
            </div>
        </el-card>
        
        <!-- 主要功能区域 -->
        <el-row :gutter="24">
            <!-- 数据查询表单 -->
            <el-col :span="12">
                <el-card shadow="hover" style="border-radius: 12px;" class="animate__animated animate__fadeInLeft">
                    <template #header>
                        <div style="display: flex; align-items: center;">
                            <el-icon size="18" style="margin-right: 8px;"><Search /></el-icon>
                            <span style="font-weight: 600;">数据查询</span>
                        </div>
                    </template>
                    <el-form :model="queryForm" label-width="100px" style="padding: 20px 0;">
                        <el-form-item label="达人ID">
                            <el-input
                                v-model="queryForm.talentId"
                                placeholder="请输入达人ID或链接"
                                size="large"
                                clearable
                            >
                                <template #prepend>
                                    <el-icon><User /></el-icon>
                                </template>
                            </el-input>
                        </el-form-item>
                        
                        <el-form-item label="平台选择">
                            <el-select v-model="queryForm.platform" placeholder="选择平台" size="large" style="width: 100%;">
                                <el-option label="抖音" value="douyin"></el-option>
                                <el-option label="快手" value="kuaishou"></el-option>
                                <el-option label="小红书" value="xiaohongshu"></el-option>
                                <el-option label="B站" value="bilibili"></el-option>
                            </el-select>
                        </el-form-item>
                        
                        <el-form-item label="时间范围">
                            <el-date-picker
                                v-model="queryForm.dateRange"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                style="width: 100%;"
                                size="large"
                            />
                        </el-form-item>
                        
                        <el-form-item label="数据类型">
                            <el-checkbox-group v-model="queryForm.dataTypes">
                                <el-checkbox label="基础信息">基础信息</el-checkbox>
                                <el-checkbox label="粉丝数据">粉丝数据</el-checkbox>
                                <el-checkbox label="作品数据">作品数据</el-checkbox>
                                <el-checkbox label="商业数据">商业数据</el-checkbox>
                            </el-checkbox-group>
                        </el-form-item>
                        
                        <el-form-item>
                            <el-button
                                type="primary"
                                size="large"
                                style="width: 100%;"
                                @click="startExtraction"
                                :loading="extracting"
                            >
                                <el-icon style="margin-right: 8px;"><Download /></el-icon>
                                {{ extracting ? '提取中...' : '开始提取' }}
                            </el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </el-col>
            
            <!-- 提取结果 -->
            <el-col :span="12">
                <el-card shadow="hover" style="border-radius: 12px;" class="animate__animated animate__fadeInRight">
                    <template #header>
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <div style="display: flex; align-items: center;">
                                <el-icon size="18" style="margin-right: 8px;"><Document /></el-icon>
                                <span style="font-weight: 600;">提取结果</span>
                            </div>
                            <el-button v-if="extractionResults.length > 0" type="primary" size="small" @click="exportData">
                                <el-icon style="margin-right: 4px;"><Download /></el-icon>
                                导出
                            </el-button>
                        </div>
                    </template>
                    <div style="min-height: 400px;">
                        <div v-if="extractionResults.length === 0" style="padding: 40px 0;">
                            <el-empty description="暂无数据，请先进行数据提取" :image-size="120"></el-empty>
                        </div>
                        <div v-else>
                            <el-table :data="extractionResults" style="width: 100%;" max-height="350">
                                <el-table-column prop="name" label="达人名称" width="120"></el-table-column>
                                <el-table-column prop="platform" label="平台" width="80"></el-table-column>
                                <el-table-column prop="fans" label="粉丝数" width="100"></el-table-column>
                                <el-table-column prop="status" label="状态" width="80">
                                    <template #default="scope">
                                        <el-tag :type="scope.row.status === '成功' ? 'success' : 'danger'" size="small">
                                            {{ scope.row.status }}
                                        </el-tag>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                </el-card>
            </el-col>
        </el-row>
        
        <!-- 提取进度 -->
        <el-card v-if="extracting" shadow="never" style="margin-top: 24px;" class="animate__animated animate__fadeInUp">
            <template #header>
                <div style="display: flex; align-items: center;">
                    <el-icon size="18" style="margin-right: 8px;"><Loading /></el-icon>
                    <span style="font-weight: 600;">提取进度</span>
                </div>
            </template>
            <el-progress :percentage="extractionProgress" :status="extractionProgress === 100 ? 'success' : null">
                <template #default="{ percentage }">
                    <span style="font-size: 14px;">{{ percentage }}% - {{ extractionStatus }}</span>
                </template>
            </el-progress>
        </el-card>
        
        <!-- 历史记录 -->
        <el-card shadow="never" style="margin-top: 24px;" class="animate__animated animate__fadeInUp" style="animation-delay: 0.4s;">
            <template #header>
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <div style="display: flex; align-items: center;">
                        <el-icon size="18" style="margin-right: 8px;"><Clock /></el-icon>
                        <span style="font-weight: 600;">提取历史</span>
                    </div>
                    <el-button type="text" @click="clearHistory">清空历史</el-button>
                </div>
            </template>
            <el-table :data="extractionHistory" style="width: 100%;">
                <el-table-column prop="time" label="提取时间" width="180"></el-table-column>
                <el-table-column prop="talentId" label="达人ID" width="150"></el-table-column>
                <el-table-column prop="platform" label="平台" width="100"></el-table-column>
                <el-table-column prop="dataTypes" label="数据类型" width="200"></el-table-column>
                <el-table-column prop="status" label="状态" width="100">
                    <template #default="scope">
                        <el-tag :type="scope.row.status === '成功' ? 'success' : 'danger'" size="small">
                            {{ scope.row.status }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作">
                    <template #default="scope">
                        <el-button type="text" size="small" @click="reExtract(scope.row)">重新提取</el-button>
                        <el-button type="text" size="small" @click="viewDetails(scope.row)">查看详情</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>

    <script>
        const { createApp, ref, reactive } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        const app = createApp({
            setup() {
                const extracting = ref(false);
                const extractionProgress = ref(0);
                const extractionStatus = ref('');
                const extractionResults = ref([]);
                
                const queryForm = reactive({
                    talentId: '',
                    platform: '',
                    dateRange: '',
                    dataTypes: ['基础信息']
                });
                
                const extractionHistory = ref([
                    {
                        time: '2024-01-01 14:30:25',
                        talentId: 'talent123',
                        platform: '抖音',
                        dataTypes: '基础信息, 粉丝数据',
                        status: '成功'
                    },
                    {
                        time: '2024-01-01 13:15:10',
                        talentId: 'talent456',
                        platform: '小红书',
                        dataTypes: '基础信息, 作品数据',
                        status: '失败'
                    }
                ]);
                
                const startExtraction = async () => {
                    if (!queryForm.talentId || !queryForm.platform) {
                        ElMessage.warning('请填写完整的查询信息');
                        return;
                    }
                    
                    extracting.value = true;
                    extractionProgress.value = 0;
                    extractionStatus.value = '开始提取...';
                    
                    // 模拟提取过程
                    const steps = [
                        '连接蝉妈妈平台...',
                        '验证达人信息...',
                        '获取基础数据...',
                        '分析粉丝数据...',
                        '处理作品信息...',
                        '生成报告...'
                    ];
                    
                    for (let i = 0; i < steps.length; i++) {
                        extractionStatus.value = steps[i];
                        extractionProgress.value = Math.floor((i + 1) / steps.length * 100);
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                    
                    // 添加提取结果
                    extractionResults.value.push({
                        name: `达人_${queryForm.talentId}`,
                        platform: queryForm.platform,
                        fans: Math.floor(Math.random() * 1000000) + 'W',
                        status: '成功'
                    });
                    
                    // 添加到历史记录
                    extractionHistory.value.unshift({
                        time: new Date().toLocaleString('zh-CN'),
                        talentId: queryForm.talentId,
                        platform: queryForm.platform,
                        dataTypes: queryForm.dataTypes.join(', '),
                        status: '成功'
                    });
                    
                    extracting.value = false;
                    ElMessage.success('数据提取完成！');
                };
                
                const exportData = () => {
                    ElMessage.success('数据导出成功！');
                };
                
                const clearHistory = async () => {
                    try {
                        await ElMessageBox.confirm('确定要清空所有历史记录吗？', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        });
                        extractionHistory.value = [];
                        ElMessage.success('历史记录已清空');
                    } catch {
                        // 用户取消
                    }
                };
                
                const reExtract = (row) => {
                    queryForm.talentId = row.talentId;
                    queryForm.platform = row.platform;
                    ElMessage.info('已填充表单，请点击开始提取');
                };
                
                const viewDetails = (row) => {
                    ElMessage.info(`查看 ${row.talentId} 的详细信息`);
                };
                
                return {
                    queryForm,
                    extracting,
                    extractionProgress,
                    extractionStatus,
                    extractionResults,
                    extractionHistory,
                    startExtraction,
                    exportData,
                    clearHistory,
                    reExtract,
                    viewDetails
                };
            }
        });

        // 注册所有图标
        Object.keys(ElementPlusIconsVue).forEach(key => {
            app.component(key, ElementPlusIconsVue[key]);
        });

        app.use(ElementPlus).mount('#app');
    </script>
</body>
</html>
