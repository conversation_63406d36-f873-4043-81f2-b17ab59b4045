<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>达人数据提取</title>
    
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Element Plus Icons -->
    <script src="//unpkg.com/@element-plus/icons-vue"></script>
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    
    <style>
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
            background: #f8fafc;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        }

        /* 子界面元素动画 */
        @keyframes smoothSlideIn {
            0% {
                transform: translateY(30px) scale(0.95);
                opacity: 0;
            }
            100% {
                transform: translateY(0) scale(1);
                opacity: 1;
            }
        }

        .animate-slide-in {
            animation: smoothSlideIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .animate-delay-1 { animation-delay: 0.15s; }
        .animate-delay-2 { animation-delay: 0.3s; }
        .animate-delay-3 { animation-delay: 0.45s; }
        .animate-delay-4 { animation-delay: 0.6s; }

        /* 上传区域样式 */
        .upload-area {
            border: 1px dashed #d1d5db;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            background: #fafbfc;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #3b82f6;
            background: #f0f9ff;
        }

        .upload-area.dragover {
            border-color: #3b82f6;
            background: #eff6ff;
        }

        /* 搜索卡片样式 */
        .search-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        /* 表格卡片样式 */
        .table-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div id="app" style="padding: 24px;">
        <!-- 文件选择区域 -->
        <div class="animate-slide-in" style="margin-bottom: 16px;">
            <div class="upload-area" style="display: flex; align-items: center; justify-content: space-between;">
                <div style="display: flex; align-items: center;">
                    <el-icon size="24" style="color: #3b82f6; margin-right: 8px;"><Upload /></el-icon>
                    <span style="font-size: 14px; color: #374151;">
                        {{ selectedFile ? selectedFile.file_name : '请选择Excel文件进行上传' }}
                    </span>
                </div>
                <div style="display: flex; gap: 8px;">
                    <el-button type="primary" @click="selectFile" :loading="fileSelecting">
                        <el-icon style="margin-right: 4px;"><FolderOpened /></el-icon>
                        选择文件
                    </el-button>
                    <el-button
                        v-if="selectedFile"
                        type="success"
                        @click="uploadFile"
                        :loading="tableLoading"
                    >
                        <el-icon style="margin-right: 4px;"><Upload /></el-icon>
                        上传文件
                    </el-button>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="animate-slide-in animate-delay-1">
            <el-card shadow="hover" class="table-card">
                <template #header>
                    <div style="display: flex; align-items: center; justify-content: space-between; flex-wrap: wrap; gap: 16px;">
                        <div style="display: flex; align-items: center;">
                            <el-icon size="18" style="margin-right: 8px; color: #3b82f6;"><Grid /></el-icon>
                            <span style="font-weight: 600; font-size: 16px;">联系人数据列表</span>
                            <el-tag type="info" size="small" style="margin-left: 12px;">共 {{ total }} 条数据</el-tag>
                        </div>

                        <!-- 搜索区域集成到表格头部 -->
                        <div style="display: flex; align-items: center; gap: 12px; flex-wrap: wrap;">
                            <el-input
                                v-model="searchForm.fileName"
                                placeholder="文件名"
                                clearable
                                size="small"
                                style="width: 120px;"
                            >
                                <template #prefix>
                                    <el-icon><Document /></el-icon>
                                </template>
                            </el-input>
                            <el-input
                                v-model="searchForm.description"
                                placeholder="简介信息"
                                clearable
                                size="small"
                                style="width: 120px;"
                            >
                                <template #prefix>
                                    <el-icon><InfoFilled /></el-icon>
                                </template>
                            </el-input>
                            <el-input
                                v-model="searchForm.phone"
                                placeholder="手机号"
                                clearable
                                size="small"
                                style="width: 120px;"
                            >
                                <template #prefix>
                                    <el-icon><Phone /></el-icon>
                                </template>
                            </el-input>
                            <el-input
                                v-model="searchForm.wechat"
                                placeholder="微信号"
                                clearable
                                size="small"
                                style="width: 120px;"
                            >
                                <template #prefix>
                                    <el-icon><ChatDotRound /></el-icon>
                                </template>
                            </el-input>
                            <el-button size="small" @click="handleSearch" type="primary">
                                <el-icon><Search /></el-icon>
                            </el-button>
                            <el-button size="small" @click="resetSearch">
                                <el-icon><Refresh /></el-icon>
                            </el-button>
                            <el-button size="small" @click="exportData">
                                <el-icon><Download /></el-icon>
                            </el-button>
                        </div>
                    </div>
                </template>
                <el-table
                    :data="tableData"
                    style="width: 100%"
                    v-loading="tableLoading"
                    element-loading-text="数据加载中..."
                    stripe
                    border
                >
                    <el-table-column prop="id" label="ID" width="80" align="center"></el-table-column>
                    <el-table-column prop="originalFileName" label="原始文件名" min-width="200">
                        <template #default="scope">
                            <div style="display: flex; align-items: center;">
                                <el-icon style="margin-right: 8px; color: #3b82f6;"><Document /></el-icon>
                                {{ scope.row.originalFileName }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="description" label="简介信息" min-width="250">
                        <template #default="scope">
                            <el-tooltip :content="scope.row.description" placement="top" :disabled="!scope.row.description || scope.row.description.length < 30">
                                <span>{{ scope.row.description || '-' }}</span>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                    <el-table-column prop="phone" label="手机号" width="140">
                        <template #default="scope">
                            <div v-if="scope.row.phone" style="display: flex; align-items: center;">
                                <el-icon style="margin-right: 4px; color: #10b981;"><Phone /></el-icon>
                                {{ scope.row.phone }}
                            </div>
                            <span v-else style="color: #9ca3af;">-</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="wechat" label="微信号" width="140">
                        <template #default="scope">
                            <div v-if="scope.row.wechat" style="display: flex; align-items: center;">
                                <el-icon style="margin-right: 4px; color: #10b981;"><ChatDotRound /></el-icon>
                                {{ scope.row.wechat }}
                            </div>
                            <span v-else style="color: #9ca3af;">-</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="120" align="center">
                        <template #default="scope">
                            <el-button type="primary" size="small" text @click="viewDetail(scope.row)">
                                <el-icon style="margin-right: 4px;"><View /></el-icon>
                                查看
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>

            </el-card>
        </div>

        <!-- 分页 -->
        <div class="animate-slide-in animate-delay-2" style="text-align: center; margin-top: 16px;">
            <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[5, 10, 20]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                background
            />
        </div>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>

    <script>
        const { createApp, ref, reactive } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        const app = createApp({
            setup() {
                // 搜索表单
                const searchForm = reactive({
                    fileName: '',
                    description: '',
                    phone: '',
                    wechat: ''
                });

                // 表格数据
                const tableData = ref([
                    {
                        id: 1,
                        originalFileName: 'contacts_2024_01.xlsx',
                        description: '2024年1月份客户联系人信息表',
                        phone: '13800138001',
                        wechat: 'user001'
                    },
                    {
                        id: 2,
                        originalFileName: 'leads_data.xlsx',
                        description: '潜在客户信息收集表，包含详细的联系方式和需求描述',
                        phone: '13800138002',
                        wechat: 'user002'
                    },
                    {
                        id: 3,
                        originalFileName: 'customer_info.xlsx',
                        description: '客户基础信息表',
                        phone: '',
                        wechat: 'user003'
                    },
                    {
                        id: 4,
                        originalFileName: 'contact_list.xlsx',
                        description: '',
                        phone: '***********',
                        wechat: ''
                    },
                    {
                        id: 5,
                        originalFileName: 'business_contacts.xlsx',
                        description: '商务联系人信息汇总表',
                        phone: '***********',
                        wechat: 'user005'
                    }
                ]);

                // 分页数据
                const currentPage = ref(1);
                const pageSize = ref(5);
                const total = ref(5);
                const tableLoading = ref(false);

                // 文件选择相关
                const selectedFile = ref(null);
                const fileSelecting = ref(false);

                // 搜索功能
                const handleSearch = () => {
                    tableLoading.value = true;
                    setTimeout(() => {
                        tableLoading.value = false;
                        ElMessage.success('搜索完成');
                    }, 1000);
                };

                const resetSearch = () => {
                    Object.keys(searchForm).forEach(key => {
                        searchForm[key] = '';
                    });
                    ElMessage.info('搜索条件已重置');
                };

                // 分页功能
                const handleSizeChange = (val) => {
                    pageSize.value = val;
                    currentPage.value = 1;
                };

                const handleCurrentChange = (val) => {
                    currentPage.value = val;
                };

                // 文件选择功能
                const selectFile = async () => {
                    try {
                        fileSelecting.value = true;

                        // 调用Python API打开文件选择对话框
                        const result = await window.pywebview.api.select_excel_file();

                        console.log('文件选择结果:', result);

                        if (result.success) {
                            selectedFile.value = result;
                            ElMessage.success(`已选择文件: ${result.file_name}`);
                        } else {
                            ElMessage.info(result.message || '未选择文件');
                        }
                    } catch (error) {
                        console.error('文件选择错误:', error);
                        ElMessage.error('文件选择失败');
                    } finally {
                        fileSelecting.value = false;
                    }
                };

                // 上传文件
                const uploadFile = async () => {
                    if (!selectedFile.value) {
                        ElMessage.warning('请先选择文件');
                        return;
                    }

                    try {
                        tableLoading.value = true;

                        console.log('开始上传文件:', selectedFile.value.file_path);

                        // 调用Python API处理文件
                        const result = await window.pywebview.api.handle_upload_excel(selectedFile.value.file_path);

                        console.log('上传结果:', result);

                        if (result.success) {
                            ElMessage.success(`文件上传成功！文件已保存到: ${result.file_path}`);
                            // 清空选择的文件
                            selectedFile.value = null;
                        } else {
                            ElMessage.error(result.message || '文件上传失败');
                        }
                    } catch (error) {
                        console.error('文件上传错误:', error);
                        ElMessage.error('文件上传失败');
                    } finally {
                        tableLoading.value = false;
                    }
                };

                // 其他功能
                const exportData = () => {
                    ElMessage.success('数据导出功能开发中...');
                };

                const viewDetail = (row) => {
                    ElMessage.info(`查看详情: ${row.originalFileName}`);
                };

                return {
                    searchForm,
                    tableData,
                    currentPage,
                    pageSize,
                    total,
                    tableLoading,
                    selectedFile,
                    fileSelecting,
                    handleSearch,
                    resetSearch,
                    handleSizeChange,
                    handleCurrentChange,
                    selectFile,
                    uploadFile,
                    exportData,
                    viewDetail
                };
            }
        });

        // 注册所有图标
        Object.keys(ElementPlusIconsVue).forEach(key => {
            app.component(key, ElementPlusIconsVue[key]);
        });

        app.use(ElementPlus).mount('#app');
    </script>
</body>
</html>
