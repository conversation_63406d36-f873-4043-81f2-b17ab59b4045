#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库测试脚本
用于验证数据库表结构、数据插入、查询等功能
"""

import sqlite3
import os
import json
from datetime import datetime
from typing import List, Dict, Any
from sqlite3_util import (
    init_database, 
    table_exists, 
    query_table, 
    batch_insert,
    create_table
)

class DatabaseTester:
    def __init__(self, db_path: str = 'system.db'):
        self.db_path = db_path
        
    def print_header(self, title: str):
        """打印测试标题"""
        print(f"\n{'='*60}")
        print(f"🧪 {title}")
        print(f"{'='*60}")
        
    def print_result(self, success: bool, message: str):
        """打印测试结果"""
        status = "✅" if success else "❌"
        print(f"{status} {message}")
        
    def test_database_init(self):
        """测试数据库初始化"""
        self.print_header("数据库初始化测试")
        
        try:
            # 删除现有数据库文件（如果存在）
            if os.path.exists(self.db_path):
                os.remove(self.db_path)
                print(f"🗑️  删除现有数据库文件: {self.db_path}")
            
            # 初始化数据库
            result = init_database(self.db_path)
            self.print_result(result, f"数据库初始化: {self.db_path}")
            
            # 验证文件是否创建
            file_exists = os.path.exists(self.db_path)
            self.print_result(file_exists, f"数据库文件存在: {file_exists}")
            
            return result and file_exists
            
        except Exception as e:
            self.print_result(False, f"数据库初始化失败: {str(e)}")
            return False
    
    def test_table_structure(self):
        """测试表结构"""
        self.print_header("表结构测试")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            print(f"📋 数据库中的表: {tables}")
            
            # 测试每个表的结构
            for table_name in tables:
                print(f"\n📊 表 '{table_name}' 结构:")
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                
                for col in columns:
                    cid, name, type_, notnull, default, pk = col
                    constraints = []
                    if pk:
                        constraints.append("PRIMARY KEY")
                    if notnull:
                        constraints.append("NOT NULL")
                    if default:
                        constraints.append(f"DEFAULT {default}")
                    
                    constraint_str = f" ({', '.join(constraints)})" if constraints else ""
                    print(f"   {name}: {type_}{constraint_str}")
            
            conn.close()
            self.print_result(True, f"表结构检查完成，共 {len(tables)} 个表")
            return True
            
        except Exception as e:
            self.print_result(False, f"表结构测试失败: {str(e)}")
            return False
    
    def test_tokens_operations(self):
        """测试tokens表操作"""
        self.print_header("Tokens表操作测试")
        
        try:
            # 1. 插入测试数据
            test_tokens = [
                ("test_token_001", datetime.now().isoformat()),
                ("test_token_002", datetime.now().isoformat()),
                ("test_token_003", datetime.now().isoformat()),
            ]
            
            print("📝 插入测试token数据...")
            inserted_count = batch_insert(
                db_path=self.db_path,
                table_name='tokens',
                field_names=['token', 'create_time'],
                data=test_tokens
            )
            self.print_result(inserted_count > 0, f"插入 {inserted_count} 条token记录")
            
            # 2. 查询所有tokens
            print("\n🔍 查询所有tokens:")
            tokens = query_table(self.db_path, 'tokens', order_by='create_time DESC')
            for i, token in enumerate(tokens, 1):
                print(f"   {i}. ID:{token['id']} Token:{token['token']} Time:{token['create_time']}")
            
            # 3. 条件查询
            print("\n🔍 条件查询 (token包含'001'):")
            filtered_tokens = query_table(
                self.db_path, 
                'tokens', 
                where="token LIKE ?", 
                params=('%001%',)
            )
            for token in filtered_tokens:
                print(f"   匹配: {token['token']}")
            
            # 4. 统计总数
            count_result = query_table(
                self.db_path, 
                'tokens', 
                columns=['COUNT(*) as total']
            )
            total_count = count_result[0]['total'] if count_result else 0
            print(f"\n📊 tokens表总记录数: {total_count}")
            
            self.print_result(True, "tokens表操作测试完成")
            return True
            
        except Exception as e:
            self.print_result(False, f"tokens表操作测试失败: {str(e)}")
            return False
    
    def test_users_operations(self):
        """测试users表操作"""
        self.print_header("Users表操作测试")
        
        try:
            # 1. 插入测试数据
            test_users = [
                ("test_file.xlsx", "测试用户1", "这是测试用户1的简介", "test_unique_1", "test_cmm_1", datetime.now().isoformat()),
                ("test_file.xlsx", "测试用户2", "这是测试用户2的简介", "test_unique_2", "test_cmm_2", datetime.now().isoformat()),
                ("test_file.xlsx", "测试用户3", "这是测试用户3的简介", "test_unique_3", "test_cmm_3", datetime.now().isoformat()),
            ]
            
            print("📝 插入测试用户数据...")
            inserted_count = batch_insert(
                db_path=self.db_path,
                table_name='users',
                field_names=['file_name', 'username', 'intro', 'unique_id', 'cmm_id', 'create_time'],
                data=test_users
            )
            self.print_result(inserted_count > 0, f"插入 {inserted_count} 条用户记录")
            
            # 2. 查询最新用户
            print("\n🔍 查询最新3个用户:")
            users = query_table(self.db_path, 'users', order_by='create_time DESC', limit=3)
            for i, user in enumerate(users, 1):
                print(f"   {i}. {user['username']} ({user['unique_id']}) - {user['intro'][:20]}...")
            
            # 3. 搜索用户
            print("\n🔍 搜索用户 (用户名包含'测试'):")
            search_users = query_table(
                self.db_path, 
                'users', 
                where="username LIKE ?", 
                params=('%测试%',)
            )
            for user in search_users:
                print(f"   匹配: {user['username']}")
            
            self.print_result(True, "users表操作测试完成")
            return True
            
        except Exception as e:
            self.print_result(False, f"users表操作测试失败: {str(e)}")
            return False
    
    def test_crud_operations(self):
        """测试完整的增删改查操作"""
        self.print_header("CRUD操作测试")

        try:
            # 1. 增加 (Create) - 插入token
            print("📝 测试插入操作...")
            test_token = f"test_token_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            create_time = datetime.now().isoformat()

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute(
                "INSERT INTO tokens (token, create_time) VALUES (?, ?)",
                (test_token, create_time)
            )
            conn.commit()
            insert_id = cursor.lastrowid
            self.print_result(True, f"插入token成功，ID: {insert_id}")

            # 2. 查询 (Read) - 查找刚插入的token
            print("\n🔍 测试查询操作...")
            cursor.execute("SELECT * FROM tokens WHERE id = ?", (insert_id,))
            result = cursor.fetchone()
            if result:
                print(f"   查询结果: ID={result[0]}, Token={result[1]}, Time={result[2]}")
                self.print_result(True, "查询token成功")
            else:
                self.print_result(False, "查询token失败")

            # 3. 修改 (Update) - 更新token
            print("\n✏️ 测试更新操作...")
            updated_token = f"updated_{test_token}"
            cursor.execute(
                "UPDATE tokens SET token = ? WHERE id = ?",
                (updated_token, insert_id)
            )
            conn.commit()
            affected_rows = cursor.rowcount
            self.print_result(affected_rows > 0, f"更新token成功，影响行数: {affected_rows}")

            # 验证更新
            cursor.execute("SELECT token FROM tokens WHERE id = ?", (insert_id,))
            updated_result = cursor.fetchone()
            if updated_result and updated_result[0] == updated_token:
                self.print_result(True, f"验证更新成功: {updated_result[0]}")

            # 4. 删除 (Delete) - 删除测试token
            print("\n🗑️ 测试删除操作...")
            cursor.execute("DELETE FROM tokens WHERE id = ?", (insert_id,))
            conn.commit()
            deleted_rows = cursor.rowcount
            self.print_result(deleted_rows > 0, f"删除token成功，影响行数: {deleted_rows}")

            # 验证删除
            cursor.execute("SELECT * FROM tokens WHERE id = ?", (insert_id,))
            delete_verify = cursor.fetchone()
            self.print_result(delete_verify is None, "验证删除成功")

            conn.close()
            self.print_result(True, "CRUD操作测试完成")
            return True

        except Exception as e:
            self.print_result(False, f"CRUD操作测试失败: {str(e)}")
            return False

    def test_token_management(self):
        """测试token管理功能"""
        self.print_header("Token管理功能测试")

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 1. 批量插入多个token
            print("📝 批量插入token...")
            tokens_data = [
                (f"batch_token_{i}", datetime.now().isoformat())
                for i in range(1, 6)
            ]

            cursor.executemany(
                "INSERT INTO tokens (token, create_time) VALUES (?, ?)",
                tokens_data
            )
            conn.commit()
            self.print_result(True, f"批量插入 {len(tokens_data)} 个token")

            # 2. 查询最新的token
            print("\n🔍 查询最新token...")
            cursor.execute(
                "SELECT * FROM tokens ORDER BY create_time DESC LIMIT 1"
            )
            latest_token = cursor.fetchone()
            if latest_token:
                print(f"   最新token: {latest_token[1]} (创建时间: {latest_token[2]})")
                self.print_result(True, "查询最新token成功")

            # 3. 统计token数量
            print("\n📊 统计token数量...")
            cursor.execute("SELECT COUNT(*) FROM tokens")
            token_count = cursor.fetchone()[0]
            print(f"   总token数量: {token_count}")

            # 4. 查找重复token
            print("\n🔍 检查重复token...")
            cursor.execute("""
                SELECT token, COUNT(*) as count
                FROM tokens
                GROUP BY token
                HAVING COUNT(*) > 1
            """)
            duplicates = cursor.fetchall()
            if duplicates:
                print(f"   发现 {len(duplicates)} 个重复token:")
                for token, count in duplicates:
                    print(f"     {token}: {count} 次")
            else:
                print("   没有发现重复token")

            # 5. 清理测试数据
            print("\n🧹 清理测试数据...")
            cursor.execute("DELETE FROM tokens WHERE token LIKE 'batch_token_%'")
            conn.commit()
            cleaned_count = cursor.rowcount
            self.print_result(True, f"清理了 {cleaned_count} 条测试数据")

            conn.close()
            self.print_result(True, "Token管理功能测试完成")
            return True

        except Exception as e:
            self.print_result(False, f"Token管理功能测试失败: {str(e)}")
            return False

    def test_custom_query(self, sql: str, params: tuple = None):
        """执行自定义SQL查询"""
        self.print_header(f"自定义查询: {sql[:50]}...")

        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)

            # 判断是否是查询语句
            if sql.strip().upper().startswith('SELECT'):
                results = [dict(row) for row in cursor.fetchall()]
                print(f"📊 查询结果 ({len(results)} 条记录):")
                for i, result in enumerate(results[:10], 1):  # 最多显示10条
                    print(f"   {i}. {result}")

                if len(results) > 10:
                    print(f"   ... 还有 {len(results) - 10} 条记录")

                self.print_result(True, f"查询执行成功，返回 {len(results)} 条记录")
                conn.close()
                return results
            else:
                # 非查询语句（INSERT, UPDATE, DELETE等）
                conn.commit()
                affected_rows = cursor.rowcount
                print(f"📝 执行结果: 影响 {affected_rows} 行")
                self.print_result(True, f"SQL执行成功，影响 {affected_rows} 行")
                conn.close()
                return affected_rows

        except Exception as e:
            self.print_result(False, f"SQL执行失败: {str(e)}")
            return []
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始数据库全面测试...")
        
        tests = [
            ("数据库初始化", self.test_database_init),
            ("表结构检查", self.test_table_structure),
            ("Tokens表操作", self.test_tokens_operations),
            ("Users表操作", self.test_users_operations),
            ("CRUD操作测试", self.test_crud_operations),
            ("Token管理功能", self.test_token_management),
        ]
        
        results = []
        for test_name, test_func in tests:
            try:
                result = test_func()
                results.append((test_name, result))
            except Exception as e:
                print(f"❌ 测试 '{test_name}' 异常: {str(e)}")
                results.append((test_name, False))
        
        # 汇总结果
        self.print_header("测试结果汇总")
        success_count = sum(1 for _, success in results if success)
        total_count = len(results)
        
        for test_name, success in results:
            status = "✅" if success else "❌"
            print(f"{status} {test_name}")
        
        print(f"\n📊 测试完成: {success_count}/{total_count} 通过")
        return success_count == total_count

def main():
    """主函数 - 交互式测试"""
    tester = DatabaseTester()
    
    while True:
        print("\n" + "="*60)
        print("🧪 数据库测试工具")
        print("="*60)
        print("1. 运行全部测试")
        print("2. 初始化数据库")
        print("3. 检查表结构")
        print("4. 测试tokens表")
        print("5. 测试users表")
        print("6. CRUD操作测试")
        print("7. Token管理功能测试")
        print("8. 自定义SQL查询")
        print("9. 查看数据库状态")
        print("0. 退出")

        choice = input("\n请选择操作 (0-9): ").strip()
        
        if choice == '0':
            print("👋 退出测试工具")
            break
        elif choice == '1':
            tester.run_all_tests()
        elif choice == '2':
            tester.test_database_init()
        elif choice == '3':
            tester.test_table_structure()
        elif choice == '4':
            tester.test_tokens_operations()
        elif choice == '5':
            tester.test_users_operations()
        elif choice == '6':
            tester.test_crud_operations()
        elif choice == '7':
            tester.test_token_management()
        elif choice == '8':
            sql = input("请输入SQL查询语句: ").strip()
            if sql:
                tester.test_custom_query(sql)
        elif choice == '9':
            # 显示数据库状态
            tester.print_header("数据库状态")
            if os.path.exists(tester.db_path):
                print(f"📁 数据库文件: {os.path.abspath(tester.db_path)}")
                print(f"📏 文件大小: {os.path.getsize(tester.db_path)} 字节")
                
                # 显示所有表的记录数
                try:
                    conn = sqlite3.connect(tester.db_path)
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = [row[0] for row in cursor.fetchall()]
                    
                    print(f"📊 表统计:")
                    for table in tables:
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        count = cursor.fetchone()[0]
                        print(f"   {table}: {count} 条记录")
                    
                    conn.close()
                except Exception as e:
                    print(f"❌ 获取表统计失败: {str(e)}")
            else:
                print(f"❌ 数据库文件不存在: {tester.db_path}")
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
