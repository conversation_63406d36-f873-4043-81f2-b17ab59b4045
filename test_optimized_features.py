#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的功能
"""

from cmm import extract_contact_code

def test_optimized_contact_extraction():
    """测试优化后的联系方式提取"""
    print("🧪 测试优化后的联系方式提取...")
    
    # 扩展测试用例
    test_cases = [
        # 原有测试用例
        {
            'signature': '商务🌷合作：Yanyan1010666 我想把Yanyan1010666 提取出来',
            'expected': 'Yanyan1010666',
            'description': '商务合作格式'
        },
        {
            'signature': '商务💌 ：GLK88888GLK （非诚勿扰）@郭导生活号',
            'expected': 'GLK88888GLK',
            'description': '商务合作格式2'
        },
        
        # 新增测试用例 - 优化后应该能提取的
        {
            'signature': '商V：test.user123',
            'expected': 'test.user123',
            'description': '商V格式（支持点号）'
        },
        {
            'signature': 'wx：contact_2024',
            'expected': 'contact_2024',
            'description': 'wx格式（小写）'
        },
        {
            'signature': 'WX：USER-NAME',
            'expected': 'USER-NAME',
            'description': 'WX格式（大写，支持连字符）'
        },
        {
            'signature': '加我：helper999',
            'expected': 'helper999',
            'description': '加我格式'
        },
        {
            'signature': '找我：finder123',
            'expected': 'finder123',
            'description': '找我格式'
        },
        {
            'signature': 'QQ：*********',
            'expected': '*********',
            'description': 'QQ号格式'
        },
        {
            'signature': 'qq：987654321',
            'expected': '987654321',
            'description': 'qq号格式（小写）'
        },
        {
            'signature': '邮箱：<EMAIL>',
            'expected': '<EMAIL>',
            'description': '完整邮箱格式'
        },
        {
            'signature': '手机：13812345678',
            'expected': '13812345678',
            'description': '手机号格式'
        },
        {
            'signature': '电话：13987654321',
            'expected': '13987654321',
            'description': '电话格式'
        },
        {
            'signature': '关注@username123 获取更多',
            'expected': 'username123',
            'description': '@用户名格式'
        },
        {
            'signature': '联系方式（contact888）',
            'expected': 'contact888',
            'description': '括号格式'
        },
        
        # 应该被过滤的测试用例
        {
            'signature': '商务：合作',  # 包含无效关键词
            'expected': '',
            'description': '包含无效关键词（应过滤）'
        },
        {
            'signature': '微信：ab',  # 太短
            'expected': '',
            'description': '联系方式太短（应过滤）'
        },
        {
            'signature': '联系：*********0*********0*********012345',  # 太长
            'expected': '',
            'description': '联系方式太长（应过滤）'
        },
        {
            'signature': 'QQ：123',  # QQ号太短
            'expected': '',
            'description': 'QQ号太短（应过滤）'
        },
    ]
    
    print(f"📋 开始测试 {len(test_cases)} 个用例...")
    
    success_count = 0
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n🔍 测试用例 {i}: {case['description']}")
        print(f"   输入: {case['signature']}")
        
        try:
            result = extract_contact_code(case['signature'])
            expected = case['expected']
            
            print(f"   输出: '{result}'")
            print(f"   期望: '{expected}'")
            
            if result == expected:
                print(f"   ✅ 通过")
                success_count += 1
            else:
                print(f"   ❌ 失败")
                
        except Exception as e:
            print(f"   ❌ 异常: {str(e)}")
    
    print(f"\n📊 测试结果:")
    print(f"   总用例: {len(test_cases)}")
    print(f"   通过: {success_count}")
    print(f"   失败: {len(test_cases) - success_count}")
    print(f"   成功率: {success_count/len(test_cases)*100:.1f}%")
    
    return success_count, len(test_cases)

def test_real_world_cases():
    """测试真实世界的复杂案例"""
    print("\n🧪 测试真实世界复杂案例...")
    
    real_cases = [
        '一切随缘就好🌹🌹 商V ：heisaomaiyu',
        '商务💌 ：GLK88888GLK （非诚勿扰）@郭导生活号 电影《巨齿鲨1》中方动作指导 参与作品百余部',
        '🌸合作微信：flower.123 🌸 专业带货',
        '联系方式VX：contact-2024 其他勿扰',
        '商务合作：business_888 专业服务',
        '咨询V：help.999 24小时在线',
        '工作邮箱：<EMAIL> 欢迎合作',
        '手机号：*********** 工作时间联系',
        '加我QQ：********* 备注来意',
        '找我wx：user_name123 验证消息：合作',
        '关注@my_account 获取最新动态',
        '联系方式（contact.me.2024）详情私聊',
        '只是普通的个人简介，没有任何联系方式',
        '🎵音乐爱好者🎵 分享生活美好 热爱创作',
        '专业摄影师 作品展示 欢迎关注',
    ]
    
    print(f"📋 测试 {len(real_cases)} 个真实案例...")
    
    extracted_count = 0
    
    for i, signature in enumerate(real_cases, 1):
        print(f"\n📝 案例 {i}:")
        print(f"   原文: {signature}")
        
        try:
            contact = extract_contact_code(signature)
            if contact:
                print(f"   ✅ 提取到: {contact}")
                extracted_count += 1
            else:
                print(f"   ⚠️ 未提取到联系方式")
                
        except Exception as e:
            print(f"   ❌ 提取异常: {str(e)}")
    
    print(f"\n📊 真实案例统计:")
    print(f"   总案例: {len(real_cases)}")
    print(f"   成功提取: {extracted_count}")
    print(f"   提取率: {extracted_count/len(real_cases)*100:.1f}%")
    
    return extracted_count, len(real_cases)

def show_optimization_summary():
    """显示优化总结"""
    print("\n📋 优化总结:")
    print("=" * 60)
    
    print("\n🎨 1. 前端卡片优化:")
    print("   ✅ 移除刺眼的渐变背景色")
    print("   ✅ 改为清新的白色卡片设计")
    print("   ✅ 保留彩色图标背景（橙、蓝、紫、绿）")
    print("   ✅ 增加柔和的阴影效果")
    print("   ✅ 优化图标尺寸和圆角")
    print("   ✅ 改进标签样式（圆角、浅色效果）")
    
    print("\n📊 2. 前端表格优化:")
    print("   ✅ 新增联系方式列")
    print("   ✅ 带图标的联系方式显示")
    print("   ✅ 未提取时的友好提示")
    print("   ✅ 绿色主题的联系方式样式")
    
    print("\n🔍 3. 正则表达式优化:")
    print("   ✅ 支持更多联系方式格式")
    print("   ✅ 增加智能过滤逻辑")
    print("   ✅ 支持点号、连字符等特殊字符")
    print("   ✅ 增加QQ号、@用户名、括号格式")
    print("   ✅ 过滤无效关键词和异常长度")
    print("   ✅ 优化邮箱和手机号识别")
    
    print("\n📈 4. 新增支持格式:")
    formats = [
        "商V：用户名",
        "wx/WX：用户名", 
        "加我/找我：用户名",
        "QQ/qq：号码",
        "完整邮箱：<EMAIL>",
        "手机/电话：号码",
        "@用户名",
        "括号格式：（联系方式）"
    ]
    
    for fmt in formats:
        print(f"   • {fmt}")

if __name__ == "__main__":
    print("🧪 测试优化后的功能...")
    
    # 测试优化后的联系方式提取
    success1, total1 = test_optimized_contact_extraction()
    
    # 测试真实世界案例
    success2, total2 = test_real_world_cases()
    
    # 显示优化总结
    show_optimization_summary()
    
    # 总体统计
    print(f"\n🎯 总体测试结果:")
    print(f"   标准测试: {success1}/{total1} ({success1/total1*100:.1f}%)")
    print(f"   真实案例: {success2}/{total2} ({success2/total2*100:.1f}%)")
    print(f"   综合成功率: {(success1+success2)/(total1+total2)*100:.1f}%")
    
    print("\n✅ 所有优化测试完成！")
