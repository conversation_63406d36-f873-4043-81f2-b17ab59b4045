#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试code字段修复和进度显示
"""

from sqlite3_util import init_database
from apis import API
import sqlite3

def test_code_field_in_database():
    """测试数据库中的code字段"""
    print("🧪 测试数据库中的code字段...")
    
    # 初始化数据库
    print("\n1️⃣ 初始化数据库...")
    init_database()
    
    # 检查users表结构
    print("\n2️⃣ 检查users表结构...")
    try:
        conn = sqlite3.connect('system.db')
        cursor = conn.cursor()
        
        # 获取表结构
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        
        print("📋 users表字段:")
        for col in columns:
            print(f"   {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'}")
        
        # 检查是否包含code字段
        column_names = [col[1] for col in columns]
        if 'code' in column_names:
            print("✅ code字段存在")
        else:
            print("❌ code字段不存在")
        
        conn.close()
        return 'code' in column_names
        
    except Exception as e:
        print(f"❌ 检查表结构失败: {str(e)}")
        return False

def test_api_field_names():
    """测试API中的field_names配置"""
    print("\n🧪 测试API中的field_names配置...")
    
    # 创建API实例
    api = API()
    
    # 模拟数据
    test_data = {
        'file_name': 'test.xlsx',
        'username': '测试用户',
        'intro': '商务合作：test123',
        'unique_id': 'testuser',
        'cmm_id': 'test_cmm_id',
        'code': 'test123',
        'create_time': '2024-01-01 12:00:00'
    }
    
    print("📋 测试数据:")
    for key, value in test_data.items():
        print(f"   {key}: {value}")
    
    # 检查字段完整性
    expected_fields = ['file_name', 'username', 'intro', 'unique_id', 'cmm_id', 'code', 'create_time']
    missing_fields = [field for field in expected_fields if field not in test_data]
    
    if missing_fields:
        print(f"❌ 缺少字段: {missing_fields}")
        return False
    else:
        print("✅ 所有字段完整")
        return True

def test_contact_extraction_integration():
    """测试联系方式提取集成"""
    print("\n🧪 测试联系方式提取集成...")
    
    from cmm import extract_contact_code
    
    test_signatures = [
        '商务🌷合作：Yanyan1010666 我想把Yanyan1010666 提取出来',
        '商务💌 ：GLK88888GLK （非诚勿扰）@郭导生活号',
        '微信：test123456 欢迎咨询',
        '一切随缘就好🌹🌹 没有联系方式'
    ]
    
    print("📋 测试联系方式提取:")
    for i, signature in enumerate(test_signatures, 1):
        print(f"\n{i}. 输入: {signature}")
        
        try:
            code = extract_contact_code(signature)
            print(f"   输出: {code if code else '未提取'}")
            
            # 模拟数据组装
            data_row = {
                'file_name': 'test.xlsx',
                'username': f'用户{i}',
                'intro': signature,
                'unique_id': f'user{i}',
                'cmm_id': f'cmm_id_{i}',
                'code': code,
                'create_time': '2024-01-01 12:00:00'
            }
            
            print(f"   数据组装: code='{data_row['code']}'")
            
        except Exception as e:
            print(f"   ❌ 异常: {str(e)}")
    
    return True

def test_database_insert_with_code():
    """测试带code字段的数据库插入"""
    print("\n🧪 测试带code字段的数据库插入...")
    
    try:
        conn = sqlite3.connect('system.db')
        cursor = conn.cursor()
        
        # 测试数据
        test_data = [
            ('test1.xlsx', '用户1', '商务合作：contact1', 'user1', 'cmm1', 'contact1', '2024-01-01 12:00:00'),
            ('test2.xlsx', '用户2', '微信：wx123', 'user2', 'cmm2', 'wx123', '2024-01-01 12:01:00'),
            ('test3.xlsx', '用户3', '普通简介', 'user3', 'cmm3', '', '2024-01-01 12:02:00'),
        ]
        
        # 插入测试数据
        field_names = ['file_name', 'username', 'intro', 'unique_id', 'cmm_id', 'code', 'create_time']
        placeholders = ', '.join(['?' for _ in field_names])
        sql = f"INSERT INTO users ({', '.join(field_names)}) VALUES ({placeholders})"
        
        print(f"📝 SQL语句: {sql}")
        print(f"📋 插入 {len(test_data)} 条测试数据...")
        
        cursor.executemany(sql, test_data)
        conn.commit()
        
        # 验证插入结果
        cursor.execute("SELECT file_name, username, code FROM users WHERE file_name LIKE 'test%.xlsx' ORDER BY create_time DESC LIMIT 3")
        results = cursor.fetchall()
        
        print("📊 插入结果验证:")
        for i, (file_name, username, code) in enumerate(results, 1):
            print(f"   {i}. {file_name} - {username} - code: '{code if code else '空'}'")
        
        # 清理测试数据
        cursor.execute("DELETE FROM users WHERE file_name LIKE 'test%.xlsx'")
        conn.commit()
        print("🧹 清理测试数据完成")
        
        conn.close()
        
        print("✅ 数据库插入测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 数据库插入测试失败: {str(e)}")
        return False

def test_progress_tracking():
    """测试进度跟踪功能"""
    print("\n🧪 测试进度跟踪功能...")
    
    api = API()
    
    # 测试进度更新
    print("📊 测试进度更新:")
    total = 10
    for i in range(1, total + 1):
        message = f"处理第{i}个项目"
        progress = api.update_processing_progress(i, total, message)
        
        if i % 3 == 0:  # 每3个显示一次详细信息
            print(f"   进度: {progress['percentage']}% - {progress['message']}")
    
    # 测试状态获取
    print("\n📋 测试状态获取:")
    status = api.get_processing_status()
    print(f"   状态: {status}")
    
    print("✅ 进度跟踪测试完成")
    return True

def run_all_tests():
    """运行所有测试"""
    print("🧪 运行code字段修复和进度显示测试...")
    print("=" * 60)
    
    tests = [
        ("数据库code字段检查", test_code_field_in_database),
        ("API字段配置检查", test_api_field_names),
        ("联系方式提取集成", test_contact_extraction_integration),
        ("数据库插入测试", test_database_insert_with_code),
        ("进度跟踪测试", test_progress_tracking),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"{'✅' if result else '❌'} {test_name}: {'通过' if result else '失败'}")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 测试总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {status} {test_name}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！code字段问题已修复！")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    run_all_tests()
