#!/bin/bash

echo "🚀 启动办公辅助系统..."
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3未安装"
    echo "请安装Python 3.7+"
    exit 1
fi

# 检查依赖
echo "📦 检查依赖..."
if ! python3 -c "import webview" &> /dev/null; then
    echo "⚠️ 正在安装依赖..."
    pip3 install -r requirements.txt
fi

# 启动应用
echo "✅ 启动应用程序..."
python3 main.py

if [ $? -ne 0 ]; then
    echo
    echo "❌ 程序异常退出"
    read -p "按Enter键退出..."
fi
