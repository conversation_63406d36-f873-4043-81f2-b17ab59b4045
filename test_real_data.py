#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实数据获取功能
"""

from apis import API
from cmm import get_token_from_db, get_real_info
from sqlite3_util import init_database

def test_get_real_info():
    """测试get_real_info方法"""
    print("🧪 测试get_real_info方法...")
    
    # 获取token
    token = get_token_from_db()
    if not token:
        print("❌ 数据库中没有token，请先登录蝉妈妈")
        return False
    
    print(f"🔑 使用token: {token[:20]}...")
    
    # 测试ID列表
    test_ids = [
        'Te4oLu6PzddK8v0S_JURlE20CMuhagMW',
        'bV-1-fGqiHdZgr2cKWwomg',
        # 可以添加更多测试ID
    ]
    
    for i, test_id in enumerate(test_ids, 1):
        print(f"\n📡 [{i}/{len(test_ids)}] 测试ID: {test_id}")
        
        try:
            result = get_real_info(test_id, token)
            
            if result:
                print(f"✅ 获取成功")
                print(f"   完整返回: {str(result)[:200]}...")
                
                # 检查是否包含signature和unique_id
                if 'signature' in result:
                    print(f"   📝 signature: {result['signature']}")
                else:
                    print(f"   ⚠️ 未找到signature字段")
                
                if 'unique_id' in result:
                    print(f"   🆔 unique_id: {result['unique_id']}")
                else:
                    print(f"   ⚠️ 未找到unique_id字段")
            else:
                print(f"❌ 获取失败，返回空数据")
                
        except Exception as e:
            print(f"❌ 获取异常: {str(e)}")
    
    return True

def test_api_token():
    """测试API获取token"""
    print("\n🧪 测试API获取token...")
    
    api = API()
    token = api.get_latest_token_from_db()
    
    if token:
        print(f"✅ API获取token成功: {token[:20]}...")
        return token
    else:
        print("❌ API获取token失败")
        return None

def simulate_process_file():
    """模拟process_file的数据处理流程"""
    print("\n🧪 模拟process_file数据处理流程...")
    
    # 模拟超链接数据
    mock_hyperlinks = [
        {
            'row': 2,
            'col': 1,
            'column_name': '用户名',
            'cell_value': '测试用户1',
            'hyperlink': 'https://www.chanmama.com/web/authorDetail/Te4oLu6PzddK8v0S_JURlE20CMuhagMW'
        },
        {
            'row': 3,
            'col': 1,
            'column_name': '用户名',
            'cell_value': '测试用户2',
            'hyperlink': 'https://www.chanmama.com/web/authorDetail/bV-1-fGqiHdZgr2cKWwomg'
        }
    ]
    
    # 模拟文件数据
    mock_file_data = {
        'name': 'test_file.xlsx'
    }
    
    print(f"📋 模拟处理 {len(mock_hyperlinks)} 个超链接...")
    
    # 获取token
    api = API()
    token = api.get_latest_token_from_db()
    if not token:
        print("❌ 未找到有效token")
        return False
    
    print(f"🔑 使用token: {token[:20]}...")
    
    # 处理每个超链接
    import re
    from datetime import datetime
    
    sqlite_data = []
    
    for i, link in enumerate(mock_hyperlinks, 1):
        # 提取ID
        url = link['hyperlink']
        match = re.search(r'authorDetail/([^/?]+)', url)
        if match:
            author_id = match.group(1)
            
            print(f"\n📡 [{i}/{len(mock_hyperlinks)}] 处理达人: {author_id}")
            
            # 默认数据
            default_intro = "专业达人，内容优质"
            default_unique_id = f"user_{author_id[:8]}"
            
            # 获取真实数据
            real_intro = default_intro
            real_unique_id = default_unique_id
            
            try:
                from cmm import get_real_info
                real_data = get_real_info(author_id, token)
                
                if real_data and real_data.get('signature') and real_data.get('unique_id'):
                    real_intro = real_data['signature']
                    real_unique_id = real_data['unique_id']
                    print(f"✅ 获取成功 - 简介: {real_intro[:30]}... | 抖音ID: {real_unique_id}")
                else:
                    print(f"⚠️ API返回数据不完整，使用默认数据")
                    print(f"   返回数据: {str(real_data)[:100]}...")
                    
            except Exception as e:
                print(f"❌ API调用失败: {str(e)}, 使用默认数据")
            
            # 组装数据
            data_row = {
                'file_name': mock_file_data['name'],
                'username': link['cell_value'],
                'intro': real_intro,
                'unique_id': real_unique_id,
                'cmm_id': author_id,
                'create_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            sqlite_data.append(data_row)
            print(f"📝 数据组装完成 - 用户: {link['cell_value']} | 抖音ID: {real_unique_id}")
    
    # 显示最终数据
    print(f"\n📊 最终数据汇总 ({len(sqlite_data)} 条):")
    for i, data in enumerate(sqlite_data, 1):
        print(f"{i}. 文件: {data['file_name']}")
        print(f"   用户: {data['username']}")
        print(f"   简介: {data['intro'][:50]}...")
        print(f"   抖音ID: {data['unique_id']}")
        print(f"   蝉妈妈ID: {data['cmm_id']}")
        print()
    
    return True

if __name__ == "__main__":
    print("🧪 测试真实数据获取功能...")
    
    # 初始化数据库
    print("\n📦 初始化数据库...")
    init_database()
    
    # 测试get_real_info方法
    test_get_real_info()
    
    # 测试API获取token
    test_api_token()
    
    # 模拟process_file流程
    simulate_process_file()
    
    print("\n✅ 所有测试完成！")
