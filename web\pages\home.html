<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页概览</title>
    
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Element Plus Icons -->
    <link rel="stylesheet" href="https://unpkg.com/@element-plus/icons-vue/dist/index.css">
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    
    <style>
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
            background: var(--el-bg-color-page);
        }
    </style>
</head>
<body>
    <div id="app" style="padding: 30px;">
        <!-- 欢迎卡片 -->
        <el-card shadow="never" style="margin-bottom: 24px;" class="animate__animated animate__fadeInUp">
            <div style="text-align: center; padding: 60px 0;">
                <el-icon size="80" color="var(--el-color-primary)"><TrophyBase /></el-icon>
                <h1 style="margin: 24px 0 12px 0; color: var(--el-text-color-primary); font-size: 32px; font-weight: 600;">欢迎使用管理系统</h1>
                <p style="color: var(--el-text-color-regular); font-size: 18px; margin: 0;">现代化的后台管理界面，让工作更高效</p>
            </div>
        </el-card>
        
        <!-- 统计卡片 -->
        <el-row :gutter="24" class="animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
            <el-col :span="6">
                <el-card shadow="hover" style="border-radius: 12px;">
                    <el-statistic title="总用户数" :value="userCount" suffix="人">
                        <template #prefix>
                            <el-icon size="24" style="color: var(--el-color-primary);"><UserFilled /></el-icon>
                        </template>
                    </el-statistic>
                </el-card>
            </el-col>

            <el-col :span="6">
                <el-card shadow="hover" style="border-radius: 12px;">
                    <el-statistic title="今日访问" :value="visitCount" suffix="次">
                        <template #prefix>
                            <el-icon size="24" style="color: var(--el-color-success);"><View /></el-icon>
                        </template>
                    </el-statistic>
                </el-card>
            </el-col>

            <el-col :span="6">
                <el-card shadow="hover" style="border-radius: 12px;">
                    <el-statistic title="数据处理" :value="processRate" suffix="%">
                        <template #prefix>
                            <el-icon size="24" style="color: var(--el-color-warning);"><TrendCharts /></el-icon>
                        </template>
                    </el-statistic>
                </el-card>
            </el-col>

            <el-col :span="6">
                <el-card shadow="hover" style="border-radius: 12px;">
                    <el-statistic title="系统状态" value="正常">
                        <template #prefix>
                            <el-icon size="24" style="color: var(--el-color-success);"><CircleCheckFilled /></el-icon>
                        </template>
                    </el-statistic>
                </el-card>
            </el-col>
        </el-row>
        
        <!-- 快捷操作 -->
        <el-card shadow="never" style="margin-top: 24px;" class="animate__animated animate__fadeInUp" style="animation-delay: 0.4s;">
            <template #header>
                <div style="display: flex; align-items: center;">
                    <el-icon size="18" style="margin-right: 8px;"><Operation /></el-icon>
                    <span style="font-weight: 600;">快捷操作</span>
                </div>
            </template>
            <el-row :gutter="16">
                <el-col :span="4" v-for="(action, index) in quickActions" :key="index">
                    <el-card shadow="hover" style="text-align: center; cursor: pointer; border-radius: 12px;" @click="handleQuickAction(action)">
                        <el-icon size="32" :style="{color: action.color}">
                            <component :is="action.icon"></component>
                        </el-icon>
                        <p style="margin: 12px 0 0 0; font-weight: 500;">{{ action.name }}</p>
                    </el-card>
                </el-col>
            </el-row>
        </el-card>
        
        <!-- 最近活动 -->
        <el-row :gutter="24" style="margin-top: 24px;">
            <el-col :span="12">
                <el-card shadow="never" class="animate__animated animate__fadeInUp" style="animation-delay: 0.6s;">
                    <template #header>
                        <div style="display: flex; align-items: center;">
                            <el-icon size="18" style="margin-right: 8px;"><Clock /></el-icon>
                            <span style="font-weight: 600;">最近活动</span>
                        </div>
                    </template>
                    <el-timeline>
                        <el-timeline-item v-for="(activity, index) in recentActivities" :key="index" :timestamp="activity.time">
                            {{ activity.content }}
                        </el-timeline-item>
                    </el-timeline>
                </el-card>
            </el-col>
            
            <el-col :span="12">
                <el-card shadow="never" class="animate__animated animate__fadeInUp" style="animation-delay: 0.8s;">
                    <template #header>
                        <div style="display: flex; align-items: center;">
                            <el-icon size="18" style="margin-right: 8px;"><TrendCharts /></el-icon>
                            <span style="font-weight: 600;">系统监控</span>
                        </div>
                    </template>
                    <div style="padding: 20px 0;">
                        <el-progress :percentage="cpuUsage" status="success" style="margin-bottom: 16px;">
                            <template #default="{ percentage }">
                                <span style="font-size: 12px;">CPU: {{ percentage }}%</span>
                            </template>
                        </el-progress>
                        <el-progress :percentage="memoryUsage" status="warning" style="margin-bottom: 16px;">
                            <template #default="{ percentage }">
                                <span style="font-size: 12px;">内存: {{ percentage }}%</span>
                            </template>
                        </el-progress>
                        <el-progress :percentage="diskUsage">
                            <template #default="{ percentage }">
                                <span style="font-size: 12px;">磁盘: {{ percentage }}%</span>
                            </template>
                        </el-progress>
                    </div>
                </el-card>
            </el-col>
        </el-row>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>

    <script>
        const { createApp, ref, onMounted } = Vue;
        const { ElMessage } = ElementPlus;

        const app = createApp({
            setup() {
                const userCount = ref(1234);
                const visitCount = ref(567);
                const processRate = ref(89);
                const cpuUsage = ref(45);
                const memoryUsage = ref(67);
                const diskUsage = ref(32);
                
                const quickActions = ref([
                    { name: '数据导入', icon: 'Upload', color: 'var(--el-color-primary)' },
                    { name: '报表生成', icon: 'DocumentCopy', color: 'var(--el-color-success)' },
                    { name: '系统设置', icon: 'Setting', color: 'var(--el-color-warning)' },
                    { name: '用户管理', icon: 'UserFilled', color: 'var(--el-color-info)' },
                    { name: '数据备份', icon: 'FolderOpened', color: 'var(--el-color-danger)' },
                    { name: '日志查看', icon: 'View', color: 'var(--el-color-primary)' }
                ]);
                
                const recentActivities = ref([
                    { time: '2024-01-01 10:30', content: '用户登录系统' },
                    { time: '2024-01-01 09:15', content: '数据同步完成' },
                    { time: '2024-01-01 08:45', content: '系统备份成功' },
                    { time: '2023-12-31 18:20', content: '生成月度报表' }
                ]);
                
                const handleQuickAction = (action) => {
                    ElMessage.success(`执行操作：${action.name}`);
                };
                
                // 模拟实时数据更新
                onMounted(() => {
                    setInterval(() => {
                        userCount.value += Math.floor(Math.random() * 3);
                        visitCount.value += Math.floor(Math.random() * 5);
                        cpuUsage.value = Math.floor(Math.random() * 30) + 30;
                        memoryUsage.value = Math.floor(Math.random() * 20) + 50;
                    }, 5000);
                });
                
                return {
                    userCount,
                    visitCount,
                    processRate,
                    cpuUsage,
                    memoryUsage,
                    diskUsage,
                    quickActions,
                    recentActivities,
                    handleQuickAction
                };
            }
        });

        // 注册所有图标
        Object.keys(ElementPlusIconsVue).forEach(key => {
            app.component(key, ElementPlusIconsVue[key]);
        });

        app.use(ElementPlus).mount('#app');
    </script>
</body>
</html>
