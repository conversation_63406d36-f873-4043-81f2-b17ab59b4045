<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页概览</title>
    
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Element Plus Icons -->
    <script src="//unpkg.com/@element-plus/icons-vue"></script>
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    
    <style>
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
            background: var(--el-bg-color-page);
        }
    </style>
</head>
<body>
    <div id="app" style="padding: 30px;">
        <!-- 欢迎卡片 -->
        <el-card shadow="never" style="margin-bottom: 24px;" class="animate__animated animate__fadeInUp">
            <div style="text-align: center; padding: 60px 0;">
                <el-icon size="80" color="var(--el-color-primary)"><Trophy /></el-icon>
                <h1 style="margin: 24px 0 12px 0; color: var(--el-text-color-primary); font-size: 32px; font-weight: 600;">欢迎使用管理系统</h1>
                <p style="color: var(--el-text-color-regular); font-size: 18px; margin: 0;">现代化的后台管理界面，让工作更高效</p>
            </div>
        </el-card>
       
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>

    <script>
        const { createApp, ref, onMounted } = Vue;
        const { ElMessage } = ElementPlus;

        const app = createApp({
            setup() {
                const userCount = ref(1234);
                const visitCount = ref(567);
                const processRate = ref(89);
                const cpuUsage = ref(45);
                const memoryUsage = ref(67);
                const diskUsage = ref(32);
                
                const quickActions = ref([
                    { name: '数据导入', icon: 'Upload', color: 'var(--el-color-primary)' },
                    { name: '报表生成', icon: 'Document', color: 'var(--el-color-success)' },
                    { name: '系统设置', icon: 'Setting', color: 'var(--el-color-warning)' },
                    { name: '用户管理', icon: 'User', color: 'var(--el-color-info)' },
                    { name: '数据备份', icon: 'Folder', color: 'var(--el-color-danger)' },
                    { name: '日志查看', icon: 'View', color: 'var(--el-color-primary)' }
                ]);
                
                const recentActivities = ref([
                    { time: '2024-01-01 10:30', content: '用户登录系统' },
                    { time: '2024-01-01 09:15', content: '数据同步完成' },
                    { time: '2024-01-01 08:45', content: '系统备份成功' },
                    { time: '2023-12-31 18:20', content: '生成月度报表' }
                ]);
                
                const handleQuickAction = (action) => {
                    ElMessage.success(`执行操作：${action.name}`);
                };
                
                // 模拟实时数据更新
                onMounted(() => {
                    setInterval(() => {
                        userCount.value += Math.floor(Math.random() * 3);
                        visitCount.value += Math.floor(Math.random() * 5);
                        cpuUsage.value = Math.floor(Math.random() * 30) + 30;
                        memoryUsage.value = Math.floor(Math.random() * 20) + 50;
                    }, 5000);
                });
                
                return {
                    userCount,
                    visitCount,
                    processRate,
                    cpuUsage,
                    memoryUsage,
                    diskUsage,
                    quickActions,
                    recentActivities,
                    handleQuickAction
                };
            }
        });

        // 注册所有图标
        Object.keys(ElementPlusIconsVue).forEach(key => {
            app.component(key, ElementPlusIconsVue[key]);
        });

        app.use(ElementPlus).mount('#app');
    </script>
</body>
</html>
