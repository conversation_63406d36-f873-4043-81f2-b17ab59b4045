<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页概览</title>
    
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Element Plus Icons -->
    <script src="//unpkg.com/@element-plus/icons-vue"></script>
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    
    <style>
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON><PERSON> UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        }

        .welcome-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none !important;
            position: relative;
            overflow: hidden;
        }

        .welcome-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: shine 4s ease-in-out infinite;
        }

        @keyframes shine {
            0%, 100% { transform: rotate(0deg); }
            50% { transform: rotate(180deg); }
        }

        .stat-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: none !important;
            background: white;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .stat-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .quick-action-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            border: none !important;
            background: white;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        }

        .quick-action-card:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }
    </style>
</head>
<body>
    <div id="app" style="padding: 24px;">
        <!-- 欢迎卡片 -->
        <el-card class="welcome-card animate__animated animate__fadeInUp" style="margin-bottom: 24px;">
            <div style="text-align: center; padding: 60px 0; position: relative; z-index: 2;">
                <div style="font-size: 80px; margin-bottom: 24px;">🎯</div>
                <h1 style="margin: 24px 0 12px 0; font-size: 36px; font-weight: 700;">欢迎使用智能管理系统</h1>
                <p style="font-size: 18px; margin: 0; opacity: 0.9;">现代化的后台管理界面，让工作更高效、更智能</p>
                <div style="margin-top: 32px;">
                    <el-button type="primary" size="large" style="background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3); color: white;">
                        <el-icon style="margin-right: 8px;"><Rocket /></el-icon>
                        开始使用
                    </el-button>
                </div>
            </div>
        </el-card>

        <!-- 统计卡片 -->
        <el-row :gutter="24" class="animate__animated animate__fadeInUp" style="animation-delay: 0.2s; margin-bottom: 24px;">
            <el-col :span="6" v-for="(stat, index) in stats" :key="index">
                <el-card class="stat-card" style="border-radius: 16px;">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div>
                            <p style="margin: 0; color: var(--el-text-color-secondary); font-size: 14px; font-weight: 500;">{{ stat.title }}</p>
                            <h3 style="margin: 8px 0 0 0; font-size: 28px; font-weight: 700; color: var(--el-text-color-primary);">{{ stat.value }}</h3>
                        </div>
                        <div :style="{background: stat.color, padding: '12px', borderRadius: '12px'}">
                            <el-icon size="24" color="white">
                                <component :is="stat.icon"></component>
                            </el-icon>
                        </div>
                    </div>
                    <div style="margin-top: 16px; display: flex; align-items: center;">
                        <el-tag :type="stat.trend === 'up' ? 'success' : 'danger'" size="small">
                            {{ stat.trend === 'up' ? '↗' : '↘' }} {{ stat.change }}
                        </el-tag>
                        <span style="margin-left: 8px; font-size: 12px; color: var(--el-text-color-secondary);">较昨日</span>
                    </div>
                </el-card>
            </el-col>
        </el-row>

        <!-- 快捷操作 -->
        <el-card shadow="never" style="margin-bottom: 24px; border-radius: 16px;" class="animate__animated animate__fadeInUp" style="animation-delay: 0.4s;">
            <template #header>
                <div style="display: flex; align-items: center;">
                    <div style="padding: 8px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 8px; margin-right: 12px;">
                        <el-icon size="18" color="white"><Operation /></el-icon>
                    </div>
                    <span style="font-weight: 600; font-size: 16px;">快捷操作</span>
                </div>
            </template>
            <el-row :gutter="16">
                <el-col :span="4" v-for="(action, index) in quickActions" :key="index">
                    <el-card class="quick-action-card" style="text-align: center; border-radius: 12px;" @click="handleQuickAction(action)">
                        <div :style="{background: action.color, padding: '16px', borderRadius: '12px', margin: '0 auto 16px', width: 'fit-content'}">
                            <el-icon size="24" color="white">
                                <component :is="action.icon"></component>
                            </el-icon>
                        </div>
                        <p style="margin: 0; font-weight: 600; color: var(--el-text-color-primary);">{{ action.name }}</p>
                        <p style="margin: 8px 0 0 0; font-size: 12px; color: var(--el-text-color-secondary);">{{ action.desc }}</p>
                    </el-card>
                </el-col>
            </el-row>
        </el-card>

        <!-- 系统信息 -->
        <el-row :gutter="24">
            <el-col :span="12">
                <el-card shadow="never" style="border-radius: 16px;" class="animate__animated animate__fadeInUp" style="animation-delay: 0.6s;">
                    <template #header>
                        <div style="display: flex; align-items: center;">
                            <div style="padding: 8px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 8px; margin-right: 12px;">
                                <el-icon size="18" color="white"><Monitor /></el-icon>
                            </div>
                            <span style="font-weight: 600; font-size: 16px;">系统监控</span>
                        </div>
                    </template>
                    <div style="padding: 16px 0;">
                        <div v-for="(monitor, index) in systemMonitor" :key="index" style="margin-bottom: 20px;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                <span style="font-size: 14px; color: var(--el-text-color-primary);">{{ monitor.name }}</span>
                                <span style="font-size: 14px; font-weight: 600; color: var(--el-text-color-primary);">{{ monitor.value }}%</span>
                            </div>
                            <el-progress :percentage="monitor.value" :status="monitor.status" :stroke-width="8" style="margin-bottom: 4px;"></el-progress>
                        </div>
                    </div>
                </el-card>
            </el-col>

            <el-col :span="12">
                <el-card shadow="never" style="border-radius: 16px;" class="animate__animated animate__fadeInUp" style="animation-delay: 0.8s;">
                    <template #header>
                        <div style="display: flex; align-items: center;">
                            <div style="padding: 8px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 8px; margin-right: 12px;">
                                <el-icon size="18" color="white"><Clock /></el-icon>
                            </div>
                            <span style="font-weight: 600; font-size: 16px;">最近活动</span>
                        </div>
                    </template>
                    <el-timeline>
                        <el-timeline-item v-for="(activity, index) in recentActivities" :key="index" :timestamp="activity.time" size="large">
                            <div style="display: flex; align-items: center;">
                                <div :style="{background: activity.color, padding: '4px', borderRadius: '6px', marginRight: '12px'}">
                                    <el-icon size="12" color="white">
                                        <component :is="activity.icon"></component>
                                    </el-icon>
                                </div>
                                <span>{{ activity.content }}</span>
                            </div>
                        </el-timeline-item>
                    </el-timeline>
                </el-card>
            </el-col>
        </el-row>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>

    <script>
        const { createApp, ref, onMounted } = Vue;
        const { ElMessage } = ElementPlus;

        const app = createApp({
            setup() {
                const stats = ref([
                    { title: '总用户数', value: '1,234', icon: 'User', color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', trend: 'up', change: '+12%' },
                    { title: '今日访问', value: '567', icon: 'View', color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', trend: 'up', change: '+8%' },
                    { title: '数据处理', value: '89%', icon: 'DataAnalysis', color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', trend: 'down', change: '-2%' },
                    { title: '系统状态', value: '正常', icon: 'CircleCheck', color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)', trend: 'up', change: '稳定' }
                ]);

                const quickActions = ref([
                    { name: '数据导入', icon: 'Upload', color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', desc: '批量导入数据' },
                    { name: '报表生成', icon: 'Document', color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', desc: '生成统计报表' },
                    { name: '系统设置', icon: 'Setting', color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', desc: '配置系统参数' },
                    { name: '用户管理', icon: 'User', color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)', desc: '管理用户权限' },
                    { name: '数据备份', icon: 'Folder', color: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', desc: '备份重要数据' },
                    { name: '日志查看', icon: 'View', color: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)', desc: '查看系统日志' }
                ]);

                const systemMonitor = ref([
                    { name: 'CPU 使用率', value: 45, status: 'success' },
                    { name: '内存使用率', value: 67, status: 'warning' },
                    { name: '磁盘使用率', value: 32, status: 'success' },
                    { name: '网络带宽', value: 78, status: 'exception' }
                ]);

                const recentActivities = ref([
                    { time: '10:30', content: '用户登录系统', icon: 'User', color: '#667eea' },
                    { time: '09:15', content: '数据同步完成', icon: 'Refresh', color: '#43e97b' },
                    { time: '08:45', content: '系统备份成功', icon: 'Folder', color: '#4facfe' },
                    { time: '昨天 18:20', content: '生成月度报表', icon: 'Document', color: '#f093fb' }
                ]);
                
                const handleQuickAction = (action) => {
                    ElMessage.success(`执行操作：${action.name}`);
                };
                
                // 模拟实时数据更新
                onMounted(() => {
                    setInterval(() => {
                        // 更新统计数据
                        stats.value[0].value = (parseInt(stats.value[0].value.replace(',', '')) + Math.floor(Math.random() * 3)).toLocaleString();
                        stats.value[1].value = (parseInt(stats.value[1].value) + Math.floor(Math.random() * 5)).toString();

                        // 更新系统监控
                        systemMonitor.value.forEach(item => {
                            item.value = Math.max(10, Math.min(95, item.value + (Math.random() - 0.5) * 10));
                            if (item.value < 50) item.status = 'success';
                            else if (item.value < 80) item.status = 'warning';
                            else item.status = 'exception';
                        });
                    }, 5000);
                });

                return {
                    stats,
                    quickActions,
                    systemMonitor,
                    recentActivities,
                    handleQuickAction
                };
            }
        });

        // 注册所有图标
        Object.keys(ElementPlusIconsVue).forEach(key => {
            app.component(key, ElementPlusIconsVue[key]);
        });

        app.use(ElementPlus).mount('#app');
    </script>
</body>
</html>
