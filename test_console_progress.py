#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试控制台进度显示和进度修复
"""

from apis import API, update_global_progress, reset_global_progress, add_console_log, GLOBAL_PROGRESS
import time

def test_console_log_function():
    """测试控制台日志功能"""
    print("🧪 测试控制台日志功能...")
    
    # 重置进度
    reset_global_progress()
    
    # 添加不同类型的日志
    add_console_log("🚀 开始处理Excel文件...", "info")
    add_console_log("📄 读取文件: test.xlsx", "info")
    add_console_log("🔗 发现 5 个超链接", "success")
    add_console_log("📡 获取达人: user123", "info")
    add_console_log("⏱️ 休眠 3 秒", "warning")
    add_console_log("✅ 完成第 1 个达人信息获取", "success")
    
    # 检查日志
    logs = GLOBAL_PROGRESS["console_logs"]
    print(f"✅ 添加了 {len(logs)} 条日志")
    
    for i, log in enumerate(logs, 1):
        print(f"   {i}. [{log['time_str']}] {log['type']}: {log['message']}")
    
    return len(logs) == 6

def test_progress_with_logs():
    """测试进度更新与日志结合"""
    print("\n🧪 测试进度更新与日志结合...")
    
    # 重置进度
    reset_global_progress()
    
    # 模拟完整的处理流程
    update_global_progress(
        status="processing",
        total=5,
        current=0,
        message="开始处理",
        start_time=time.time(),
        log_message="🚀 开始处理Excel文件..."
    )
    
    for i in range(1, 6):
        # 模拟处理每个项目
        update_global_progress(
            current=i,
            current_item=f"处理项目 {i}",
            message=f"正在处理 {i}/5",
            log_message=f"📡 [{i}/5] 获取达人: user{i}"
        )
        time.sleep(0.2)
        
        # 模拟休眠（除了最后一个）
        if i < 5:
            update_global_progress(
                current_item=f"休眠3秒...",
                log_message="⏱️ 休眠 3 秒，避免请求过快"
            )
            time.sleep(0.1)
        
        # 完成当前项目
        update_global_progress(
            current=i,
            current_item=f"已完成第{i}个项目",
            log_message=f"✅ 完成第 {i} 个达人信息获取"
        )
    
    # 完成处理
    update_global_progress(
        status="completed",
        message="处理完成",
        log_message="🎉 Excel文件处理完成！所有达人信息已获取"
    )
    
    # 检查最终状态
    final_progress = GLOBAL_PROGRESS
    print(f"✅ 最终进度: {final_progress['current']}/{final_progress['total']} ({final_progress['percentage']}%)")
    print(f"✅ 最终状态: {final_progress['status']}")
    print(f"✅ 日志条数: {len(final_progress['console_logs'])}")
    
    # 验证进度是否达到100%
    progress_100 = final_progress['percentage'] == 100.0
    status_completed = final_progress['status'] == 'completed'
    has_logs = len(final_progress['console_logs']) > 0
    
    return progress_100 and status_completed and has_logs

def test_api_get_status_with_logs():
    """测试API获取状态包含日志"""
    print("\n🧪 测试API获取状态包含日志...")
    
    api = API()
    
    # 重置并添加一些日志
    reset_global_progress()
    add_console_log("测试日志1", "info")
    add_console_log("测试日志2", "success")
    add_console_log("测试日志3", "warning")
    
    # 获取状态
    status = api.get_processing_status()
    
    print(f"✅ 状态获取成功:")
    print(f"   状态: {status['status']}")
    print(f"   日志条数: {len(status.get('console_logs', []))}")
    
    # 检查日志内容
    if 'console_logs' in status and len(status['console_logs']) > 0:
        print(f"   日志内容:")
        for i, log in enumerate(status['console_logs'], 1):
            print(f"     {i}. [{log['time_str']}] {log['type']}: {log['message']}")
        return True
    else:
        print("   ❌ 没有找到日志")
        return False

def test_log_limit():
    """测试日志数量限制"""
    print("\n🧪 测试日志数量限制...")
    
    # 重置进度
    reset_global_progress()
    
    # 添加超过限制的日志
    for i in range(60):  # 超过MAX_CONSOLE_LOGS(50)
        add_console_log(f"测试日志 {i+1}", "info")
    
    logs = GLOBAL_PROGRESS["console_logs"]
    print(f"✅ 添加60条日志后，实际保留: {len(logs)} 条")
    print(f"✅ 最早的日志: {logs[0]['message']}")
    print(f"✅ 最新的日志: {logs[-1]['message']}")
    
    # 验证日志数量不超过限制
    return len(logs) <= 50

def test_progress_100_percent():
    """专门测试进度能否达到100%"""
    print("\n🧪 专门测试进度能否达到100%...")
    
    # 重置进度
    reset_global_progress()
    
    # 模拟8个项目的处理（类似实际情况）
    total_items = 8
    update_global_progress(
        status="processing",
        total=total_items,
        current=0,
        start_time=time.time()
    )
    
    for i in range(1, total_items + 1):
        print(f"   处理项目 {i}/{total_items}")
        
        # 模拟处理过程
        update_global_progress(
            current_item=f"处理项目 {i}",
            message=f"正在处理 {i}/{total_items}"
        )
        
        # 模拟休眠（除了最后一个）
        if i < total_items:
            update_global_progress(
                current=i,
                current_item=f"休眠中...",
                message=f"已处理 {i}/{total_items} 个项目，休眠中..."
            )
        
        # 完成当前项目（包括最后一个）
        update_global_progress(
            current=i,
            current_item=f"已完成第{i}个项目",
            message=f"已处理 {i}/{total_items} 个项目"
        )
    
    # 检查最终进度
    final_progress = GLOBAL_PROGRESS
    print(f"✅ 最终进度: {final_progress['current']}/{final_progress['total']}")
    print(f"✅ 最终百分比: {final_progress['percentage']}%")
    
    # 验证是否达到100%
    return final_progress['percentage'] == 100.0

def show_improvement_summary():
    """显示改进总结"""
    print("\n" + "="*60)
    print("📋 控制台进度显示改进总结")
    print("="*60)
    
    print("\n🎯 解决的问题:")
    print("❌ 原问题: 进度只能到87.5%，最后一条数据不更新")
    print("❌ 原问题: 前端无法看到后端控制台的详细处理信息")
    print("❌ 原问题: 新增的进度卡片占用太多空间")
    
    print("\n✅ 改进方案:")
    print("1. 🔧 修复进度计算: 确保最后一个项目也更新进度到100%")
    print("2. 📺 控制台日志收集: 收集后端处理过程中的关键日志")
    print("3. 🎨 工作状态卡片集成: 在现有卡片中显示控制台输出")
    print("4. 🗑️ 删除冗余卡片: 移除新增的独立进度卡片")
    print("5. 📜 自动滚动: 控制台输出自动滚动到最新内容")
    
    print("\n🔧 技术实现:")
    print("• 进度修复: 将进度更新移到休眠判断之后")
    print("• 日志收集: add_console_log()函数收集关键操作日志")
    print("• 日志限制: 最多保留50条日志，自动清理旧日志")
    print("• 前端显示: 在工作状态卡片中显示滚动的控制台输出")
    print("• 颜色区分: 不同类型日志使用不同颜色显示")
    
    print("\n📊 日志类型:")
    print("• info: 一般信息（灰色）")
    print("• success: 成功操作（绿色）")
    print("• warning: 警告信息（橙色）")
    print("• error: 错误信息（红色）")
    
    print("\n🎮 用户体验:")
    print("✅ 进度准确: 进度条能正确显示到100%")
    print("✅ 实时反馈: 看到后端处理的详细步骤")
    print("✅ 空间优化: 不占用额外的界面空间")
    print("✅ 自动滚动: 始终显示最新的处理信息")
    print("✅ 视觉清晰: 不同类型信息有颜色区分")

def run_all_tests():
    """运行所有测试"""
    print("🧪 测试控制台进度显示和进度修复")
    print("="*60)
    
    tests = [
        ("控制台日志功能测试", test_console_log_function),
        ("进度与日志结合测试", test_progress_with_logs),
        ("API状态获取包含日志测试", test_api_get_status_with_logs),
        ("日志数量限制测试", test_log_limit),
        ("进度100%达成测试", test_progress_100_percent),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"{'✅' if result else '❌'} {test_name}: {'通过' if result else '失败'}")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {str(e)}")
            results.append((test_name, False))
    
    # 显示改进总结
    show_improvement_summary()
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 测试总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {status} {test_name}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有测试通过！控制台进度显示功能已完美实现！")
        print("\n💡 现在可以体验:")
        print("   1. 上传Excel文件开始处理")
        print("   2. 在工作状态卡片中观察实时控制台输出")
        print("   3. 看到进度条正确显示到100%")
        print("   4. 查看不同颜色的日志信息")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    run_all_tests()
