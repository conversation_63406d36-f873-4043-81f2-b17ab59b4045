#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试全局进度跟踪功能
"""

from apis import API, update_global_progress, reset_global_progress, GLOBAL_PROGRESS, PROGRESS_LOCK
import time
import threading

def test_global_progress_functions():
    """测试全局进度函数"""
    print("🧪 测试全局进度函数...")
    
    # 重置进度
    reset_global_progress()
    print(f"✅ 重置后状态: {GLOBAL_PROGRESS['status']}")
    
    # 更新进度
    update_global_progress(
        status="processing",
        total=10,
        current=0,
        message="开始测试",
        current_file="test.xlsx",
        start_time=time.time()
    )
    print(f"✅ 初始化后状态: {GLOBAL_PROGRESS['status']}")
    print(f"   总数: {GLOBAL_PROGRESS['total']}")
    print(f"   当前: {GLOBAL_PROGRESS['current']}")
    print(f"   百分比: {GLOBAL_PROGRESS['percentage']}%")
    
    # 模拟进度更新
    for i in range(1, 11):
        update_global_progress(
            current=i,
            current_item=f"处理项目 {i}",
            message=f"正在处理第 {i} 个项目"
        )
        time.sleep(0.5)  # 模拟处理时间
    
    # 完成
    update_global_progress(
        status="completed",
        message="测试完成"
    )
    print(f"✅ 完成后状态: {GLOBAL_PROGRESS['status']}")
    print(f"   预计剩余时间: {GLOBAL_PROGRESS['estimated_remaining']}")
    
    return True

def test_api_progress_methods():
    """测试API进度方法"""
    print("\n🧪 测试API进度方法...")
    
    api = API()
    
    # 重置进度
    reset_global_progress()
    
    # 测试get_processing_status
    status = api.get_processing_status()
    print(f"✅ 获取状态成功:")
    print(f"   状态: {status['status']}")
    print(f"   消息: {status['message']}")
    print(f"   是否空闲: {status['is_idle']}")
    print(f"   是否处理中: {status['is_processing']}")
    print(f"   是否完成: {status['is_completed']}")
    print(f"   是否错误: {status['is_error']}")
    
    # 测试update_processing_progress
    result = api.update_processing_progress(current=5, total=10, message="API测试")
    print(f"✅ 更新进度成功:")
    print(f"   当前: {result['current']}")
    print(f"   总数: {result['total']}")
    print(f"   百分比: {result['percentage']}%")
    
    return True

def test_thread_safety():
    """测试线程安全性"""
    print("\n🧪 测试线程安全性...")
    
    reset_global_progress()
    update_global_progress(status="processing", total=100, start_time=time.time())
    
    def worker(thread_id):
        """工作线程函数"""
        for i in range(10):
            update_global_progress(
                current=thread_id * 10 + i,
                current_item=f"线程{thread_id}-项目{i}",
                message=f"线程{thread_id}正在处理"
            )
            time.sleep(0.1)
    
    # 创建多个线程
    threads = []
    for i in range(5):
        thread = threading.Thread(target=worker, args=(i,))
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    print(f"✅ 多线程测试完成")
    print(f"   最终状态: {GLOBAL_PROGRESS['status']}")
    print(f"   最终进度: {GLOBAL_PROGRESS['current']}/{GLOBAL_PROGRESS['total']}")
    
    return True

def test_progress_polling_simulation():
    """模拟前端轮询测试"""
    print("\n🧪 模拟前端轮询测试...")
    
    api = API()
    
    # 重置并开始处理
    reset_global_progress()
    update_global_progress(
        status="processing",
        total=20,
        current=0,
        message="模拟文件处理",
        current_file="simulation.xlsx",
        start_time=time.time()
    )
    
    # 模拟后台处理
    def background_processing():
        """模拟后台处理过程"""
        for i in range(1, 21):
            update_global_progress(
                current=i,
                current_item=f"模拟处理达人 {i}",
                message=f"正在获取达人信息 ({i}/20)"
            )
            time.sleep(0.3)  # 模拟处理时间
        
        # 完成处理
        update_global_progress(
            status="completed",
            message="模拟处理完成"
        )
    
    # 启动后台处理线程
    processing_thread = threading.Thread(target=background_processing)
    processing_thread.start()
    
    # 模拟前端轮询
    polling_count = 0
    while True:
        status = api.get_processing_status()
        polling_count += 1
        
        print(f"📊 轮询 #{polling_count}: {status['current']}/{status['total']} ({status['percentage']}%) - {status['message']}")
        if status['current_item']:
            print(f"   当前项目: {status['current_item']}")
        
        # 检查是否完成
        if status['is_completed'] or status['is_error']:
            print(f"✅ 处理完成，状态: {status['status']}")
            break
        
        # 模拟轮询间隔
        time.sleep(1)
        
        # 防止无限循环
        if polling_count > 30:
            print("⚠️ 轮询超时，停止测试")
            break
    
    # 等待后台线程完成
    processing_thread.join()
    
    print(f"✅ 轮询测试完成，总共轮询 {polling_count} 次")
    
    return True

def test_error_handling():
    """测试错误处理"""
    print("\n🧪 测试错误处理...")
    
    # 模拟错误状态
    reset_global_progress()
    update_global_progress(
        status="processing",
        total=5,
        current=2,
        message="处理中...",
        start_time=time.time()
    )
    
    # 模拟错误
    update_global_progress(
        status="error",
        message="模拟处理错误：文件格式不正确"
    )
    
    api = API()
    status = api.get_processing_status()
    
    print(f"✅ 错误状态测试:")
    print(f"   状态: {status['status']}")
    print(f"   错误消息: {status['message']}")
    print(f"   是否错误: {status['is_error']}")
    
    return True

def show_progress_summary():
    """显示进度跟踪功能总结"""
    print("\n" + "="*60)
    print("📋 全局进度跟踪功能总结")
    print("="*60)
    
    print("\n🎯 解决的问题:")
    print("❌ 原问题: process_file是异步的，无法实时返回进度")
    print("❌ 原问题: 前端无法获知后端处理进度")
    print("❌ 原问题: 用户不知道处理是否卡住或完成")
    
    print("\n✅ 解决方案:")
    print("1. 🌐 全局进度变量: GLOBAL_PROGRESS字典存储实时状态")
    print("2. 🔒 线程安全: 使用PROGRESS_LOCK确保并发安全")
    print("3. 📊 实时更新: process_file中每处理一项就更新进度")
    print("4. 🔄 前端轮询: get_processing_status返回实时进度")
    print("5. 🎨 UI显示: 前端显示进度条和详细信息")
    
    print("\n🔧 技术实现:")
    print("• 全局变量: 存储状态、进度、消息等信息")
    print("• 更新函数: update_global_progress()线程安全更新")
    print("• 查询接口: get_processing_status()返回当前状态")
    print("• 前端轮询: 根据爬取间隔自动调整轮询频率")
    print("• 进度UI: 实时显示进度条、当前项目、预计时间")
    
    print("\n📊 进度信息包含:")
    print("• 状态: idle/processing/completed/error")
    print("• 进度: current/total/percentage")
    print("• 消息: 当前操作描述")
    print("• 项目: 当前处理的具体项目")
    print("• 文件: 当前处理的文件名")
    print("• 时间: 已用时间和预计剩余时间")
    
    print("\n🎮 用户体验:")
    print("✅ 实时进度: 看到具体处理到第几个达人")
    print("✅ 时间预估: 知道大概还需要多长时间")
    print("✅ 状态明确: 知道是否在处理、完成或出错")
    print("✅ 可控制: 可以停止进度监控")

def run_all_tests():
    """运行所有测试"""
    print("🧪 测试全局进度跟踪功能")
    print("="*60)
    
    tests = [
        ("全局进度函数测试", test_global_progress_functions),
        ("API进度方法测试", test_api_progress_methods),
        ("线程安全性测试", test_thread_safety),
        ("前端轮询模拟测试", test_progress_polling_simulation),
        ("错误处理测试", test_error_handling),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"{'✅' if result else '❌'} {test_name}: {'通过' if result else '失败'}")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {str(e)}")
            results.append((test_name, False))
    
    # 显示功能总结
    show_progress_summary()
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 测试总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {status} {test_name}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有测试通过！全局进度跟踪功能已就绪！")
        print("\n💡 现在可以在主应用中体验:")
        print("   1. 上传Excel文件开始处理")
        print("   2. 观察实时进度显示")
        print("   3. 查看当前处理项目和预计时间")
        print("   4. 处理完成后自动停止轮询")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    run_all_tests()
