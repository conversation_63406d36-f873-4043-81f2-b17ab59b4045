# 交互文件
import openpyxl
class API:
    def __init__(self):
        pass

    def say_hello(self, val):
        print(val)
        return "Hello, <PERSON>!"

    # 处理文件内容
    def process_file(self, file_data):
        """
        处理前端传来的文件数据，直接在内存中读取Excel内容
        :param file_data: 包含文件信息和内容的字典
        :return: 处理结果
        """
        try:
            print("=" * 50)
            print("开始处理Excel文件...")

            if not file_data:
                return {
                    "success": False,
                    "message": "未接收到文件数据"
                }

            # 检查必要字段
            required_fields = ['name', 'content']
            for field in required_fields:
                if field not in file_data:
                    return {
                        "success": False,
                        "message": f"缺少必要字段: {field}"
                    }

            print(f"文件名: {file_data['name']}")
            print(f"文件大小: {file_data.get('size', 'unknown')} bytes")
            print(f"内容数组长度: {len(file_data['content'])}")

            # 将字节数组转换为字节对象
            file_content = bytes(file_data['content'])
            print(f"转换后的字节长度: {len(file_content)}")

            # 使用BytesIO在内存中创建文件对象
            from io import BytesIO
            file_stream = BytesIO(file_content)

            # 使用openpyxl直接从内存读取Excel文件
            print("开始用openpyxl读取Excel内容...")
            workbook = openpyxl.load_workbook(file_stream)

            # 获取工作表信息
            sheet_names = workbook.sheetnames
            print(f"工作表数量: {len(sheet_names)}")
            print(f"工作表名称: {sheet_names}")

            # 读取第一个工作表
            sheet = workbook.active
            print(f"当前工作表: {sheet.title}")
            print(f"最大行数: {sheet.max_row}")
            print(f"最大列数: {sheet.max_column}")

            # 读取表头（第一行）
            headers = []
            for col in range(1, sheet.max_column + 1):
                cell_value = sheet.cell(row=1, column=col).value
                headers.append(str(cell_value) if cell_value else f"列{col}")

            print(f"表头信息: {headers}")

            # 读取前5行数据作为示例
            print("\n前5行数据:")
            sample_data = []
            for row in range(2, min(7, sheet.max_row + 1)):  # 从第2行开始，最多读取5行
                row_data = []
                for col in range(1, sheet.max_column + 1):
                    cell_value = sheet.cell(row=row, column=col).value
                    row_data.append(str(cell_value) if cell_value else "")

                sample_data.append(row_data)
                print(f"第{row}行: {row_data}")

            # 关闭工作簿
            workbook.close()

            print("=" * 50)
            print("Excel文件读取完成！")

            return {
                "success": True,
                "message": "Excel文件读取成功",
                "data": {
                    "filename": file_data['name'],
                    "sheet_names": sheet_names,
                    "current_sheet": sheet.title,
                    "max_row": sheet.max_row,
                    "max_column": sheet.max_column,
                    "headers": headers,
                    "sample_data": sample_data
                }
            }

        except Exception as e:
            print(f"Excel文件处理错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "message": f"Excel文件处理失败: {str(e)}",
                "error": str(e)
            }

  
    






