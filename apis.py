# 交互文件
import os
import shutil
from datetime import datetime
import webview

class API:
    def __init__(self):
        # 确保上传目录存在
        self.upload_base_dir = "datas/upload"
        if not os.path.exists(self.upload_base_dir):
            os.makedirs(self.upload_base_dir)

    def say_hello(self, val):
        print(val)
        return "Hello, World!"

    # 选择Excel文件
    def select_excel_file(self):
        """
        打开文件选择对话框选择Excel文件
        :return: 选择的文件路径
        """
        try:
            print("开始打开文件选择对话框...")

            # 使用 tkinter 文件对话框作为备选方案
            try:
                import tkinter as tk
                from tkinter import filedialog

                # 创建隐藏的根窗口
                root = tk.Tk()
                root.withdraw()
                root.attributes('-topmost', True)

                # 打开文件选择对话框
                file_path = filedialog.askopenfilename(
                    title="选择Excel文件",
                    filetypes=[
                        ("Excel文件", "*.xlsx *.xls"),
                        ("所有文件", "*.*")
                    ]
                )

                root.destroy()

                print(f"tkinter对话框返回结果: {file_path}")

                if file_path:
                    print(f"用户选择的文件: {file_path}")

                    # 检查文件是否存在
                    if os.path.exists(file_path):
                        return {
                            "success": True,
                            "file_path": file_path,
                            "file_name": os.path.basename(file_path)
                        }
                    else:
                        return {
                            "success": False,
                            "message": f"文件不存在: {file_path}"
                        }
                else:
                    return {
                        "success": False,
                        "message": "未选择文件"
                    }

            except ImportError:
                print("tkinter不可用，尝试使用webview文件对话框...")

                # 尝试使用webview的文件对话框
                file_types = ('Excel文件 (*.xlsx;*.xls)', 'All files (*.*)')
                result = webview.windows[0].create_file_dialog(
                    webview.OPEN_DIALOG,
                    allow_multiple=False,
                    file_types=file_types
                )

                print(f"webview对话框返回结果: {result}")

                if result and len(result) > 0:
                    file_path = result[0]
                    print(f"用户选择的文件: {file_path}")

                    if os.path.exists(file_path):
                        return {
                            "success": True,
                            "file_path": file_path,
                            "file_name": os.path.basename(file_path)
                        }
                    else:
                        return {
                            "success": False,
                            "message": f"文件不存在: {file_path}"
                        }
                else:
                    return {
                        "success": False,
                        "message": "未选择文件"
                    }

        except Exception as e:
            print(f"文件选择错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "message": f"文件选择失败: {str(e)}",
                "error": str(e)
            }

    # 监听excel上传
    def handle_upload_excel(self, file_path):
        """
        处理Excel文件上传
        :param file_path: 上传的文件路径
        :return: 处理结果
        """
        try:
            print(f"开始处理文件: {file_path}")

            # 检查文件是否存在
            if not os.path.exists(file_path):
                return {
                    "success": False,
                    "message": f"文件不存在: {file_path}",
                    "error": "文件路径无效"
                }

            # 创建以日期命名的文件夹
            today = datetime.now().strftime("%Y-%m-%d")
            date_dir = os.path.join(self.upload_base_dir, today)
            if not os.path.exists(date_dir):
                os.makedirs(date_dir)
                print(f"创建目录: {date_dir}")

            # 获取原始文件名
            original_filename = os.path.basename(file_path)

            # 生成新的文件路径
            timestamp = datetime.now().strftime("%H%M%S")
            name, ext = os.path.splitext(original_filename)
            new_filename = f"{name}_{timestamp}{ext}"
            new_file_path = os.path.join(date_dir, new_filename)

            # 复制文件到目标位置
            shutil.copy2(file_path, new_file_path)
            print(f"文件已复制到: {new_file_path}")

            return {
                "success": True,
                "message": "文件上传成功",
                "file_path": new_file_path,
                "original_filename": original_filename
            }

        except Exception as e:
            print(f"文件处理错误: {str(e)}")
            return {
                "success": False,
                "message": f"文件上传失败: {str(e)}",
                "error": str(e)
            }

    






