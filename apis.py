# 交互文件
import os
import shutil
from datetime import datetime

class API:
    def __init__(self):
        # 确保上传目录存在
        self.upload_base_dir = "datas/upload"
        if not os.path.exists(self.upload_base_dir):
            os.makedirs(self.upload_base_dir)

    def say_hello(self, val):
        print(val)
        return "Hello, World!"

    # 处理文件内容
    def process_file(self, file_data):
        """
        处理前端传来的文件数据
        :param file_data: 包含文件信息和内容的字典
        :return: 处理结果
        """
        try:
            print(f"开始处理文件: {file_data['name']}")
            print(f"文件大小: {file_data['size']} bytes")
            print(f"文件类型: {file_data['type']}")

            # 创建以日期命名的文件夹
            today = datetime.now().strftime("%Y-%m-%d")
            date_dir = os.path.join(self.upload_base_dir, today)
            if not os.path.exists(date_dir):
                os.makedirs(date_dir)
                print(f"创建目录: {date_dir}")

            # 生成文件路径
            timestamp = datetime.now().strftime("%H%M%S")
            name, ext = os.path.splitext(file_data['name'])
            new_filename = f"{name}_{timestamp}{ext}"
            file_path = os.path.join(date_dir, new_filename)

            # 将文件内容写入文件
            file_content = bytes(file_data['content'])
            with open(file_path, 'wb') as f:
                f.write(file_content)

            print(f"文件已保存到: {file_path}")

            return {
                "success": True,
                "message": "文件上传成功",
                "file_path": file_path,
                "original_filename": file_data['name']
            }

        except Exception as e:
            print(f"文件处理错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "message": f"文件处理失败: {str(e)}",
                "error": str(e)
            }

    # 监听excel上传
    def handle_upload_excel(self, file_path):
        """
        处理Excel文件上传
        :param file_path: 上传的文件路径
        :return: 处理结果
        """
        try:
            print(f"开始处理文件: {file_path}")

            # 检查文件是否存在
            if not os.path.exists(file_path):
                return {
                    "success": False,
                    "message": f"文件不存在: {file_path}",
                    "error": "文件路径无效"
                }

            # 创建以日期命名的文件夹
            today = datetime.now().strftime("%Y-%m-%d")
            date_dir = os.path.join(self.upload_base_dir, today)
            if not os.path.exists(date_dir):
                os.makedirs(date_dir)
                print(f"创建目录: {date_dir}")

            # 获取原始文件名
            original_filename = os.path.basename(file_path)

            # 生成新的文件路径
            timestamp = datetime.now().strftime("%H%M%S")
            name, ext = os.path.splitext(original_filename)
            new_filename = f"{name}_{timestamp}{ext}"
            new_file_path = os.path.join(date_dir, new_filename)

            # 复制文件到目标位置
            shutil.copy2(file_path, new_file_path)
            print(f"文件已复制到: {new_file_path}")

            return {
                "success": True,
                "message": "文件上传成功",
                "file_path": new_file_path,
                "original_filename": original_filename
            }

        except Exception as e:
            print(f"文件处理错误: {str(e)}")
            return {
                "success": False,
                "message": f"文件上传失败: {str(e)}",
                "error": str(e)
            }

    






