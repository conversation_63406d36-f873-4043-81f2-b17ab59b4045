# 交互文件
import os
import shutil
from datetime import datetime

class API:
    def __init__(self):
        # 确保上传目录存在
        self.upload_base_dir = "datas/upload"
        if not os.path.exists(self.upload_base_dir):
            os.makedirs(self.upload_base_dir)

    def say_hello(self, val):
        print(val)
        return "Hello, World!"

    # 处理文件内容
    def process_file(self, file_data):
        """
        处理前端传来的文件数据
        :param file_data: 包含文件信息和内容的字典
        :return: 处理结果
        """
        try:
            print("=" * 50)
            print("开始处理文件...")
            print(f"接收到的数据类型: {type(file_data)}")
            print(f"数据内容: {file_data}")

            if not file_data:
                return {
                    "success": False,
                    "message": "未接收到文件数据"
                }

            # 检查必要字段
            required_fields = ['name', 'size', 'content']
            for field in required_fields:
                if field not in file_data:
                    return {
                        "success": False,
                        "message": f"缺少必要字段: {field}"
                    }

            # type字段可选，如果没有则根据文件扩展名设置
            if 'type' not in file_data or not file_data['type']:
                file_name = file_data['name'].lower()
                if file_name.endswith('.xlsx'):
                    file_data['type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                elif file_name.endswith('.xls'):
                    file_data['type'] = 'application/vnd.ms-excel'
                else:
                    file_data['type'] = 'application/octet-stream'
                print(f"自动设置文件类型: {file_data['type']}")

            print(f"文件名: {file_data['name']}")
            print(f"文件大小: {file_data['size']} bytes")
            print(f"文件类型: {file_data['type']}")
            print(f"内容数组长度: {len(file_data['content'])}")

            # 创建以日期命名的文件夹
            today = datetime.now().strftime("%Y-%m-%d")
            date_dir = os.path.join(self.upload_base_dir, today)
            if not os.path.exists(date_dir):
                os.makedirs(date_dir)
                print(f"创建目录: {date_dir}")
            else:
                print(f"目录已存在: {date_dir}")

            # 生成文件路径
            timestamp = datetime.now().strftime("%H%M%S")
            name, ext = os.path.splitext(file_data['name'])
            new_filename = f"{name}_{timestamp}{ext}"
            file_path = os.path.join(date_dir, new_filename)
            print(f"目标文件路径: {file_path}")

            # 将文件内容写入文件
            try:
                file_content = bytes(file_data['content'])
                print(f"转换后的字节长度: {len(file_content)}")

                with open(file_path, 'wb') as f:
                    f.write(file_content)

                # 验证文件是否写入成功
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    print(f"文件写入成功，实际大小: {file_size} bytes")
                else:
                    return {
                        "success": False,
                        "message": "文件写入失败，文件不存在"
                    }

            except Exception as write_error:
                print(f"文件写入错误: {str(write_error)}")
                return {
                    "success": False,
                    "message": f"文件写入失败: {str(write_error)}"
                }

            print(f"文件处理完成: {file_path}")
            print("=" * 50)

            return {
                "success": True,
                "message": "文件上传成功",
                "file_path": file_path,
                "original_filename": file_data['name']
            }

        except Exception as e:
            print(f"文件处理错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "message": f"文件处理失败: {str(e)}",
                "error": str(e)
            }

  
    






