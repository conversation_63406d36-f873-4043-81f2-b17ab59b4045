# 交互文件
import openpyxl
from sqlite3_util import batch_insert, query_users, get_users_count, verify_insert_result
import threading
import time

# 全局进度跟踪变量
GLOBAL_PROGRESS = {
    "status": "idle",  # idle, processing, completed, error
    "current": 0,
    "total": 0,
    "percentage": 0,
    "current_item": "",
    "message": "系统就绪",
    "start_time": None,
    "estimated_remaining": "未知",
    "processed_items": [],  # 已处理的项目列表
    "current_file": "",
    "last_update": time.time(),
    "console_logs": []  # 控制台日志列表
}

# 进度锁，确保线程安全
PROGRESS_LOCK = threading.Lock()

# 控制台日志最大条数
MAX_CONSOLE_LOGS = 50

def add_console_log(log_message, log_type="info"):
    """
    添加控制台日志（线程安全）
    """
    global GLOBAL_PROGRESS

    with PROGRESS_LOCK:
        log_entry = {
            "timestamp": time.time(),
            "message": log_message,
            "type": log_type,  # info, success, warning, error
            "time_str": time.strftime("%H:%M:%S", time.localtime())
        }

        GLOBAL_PROGRESS["console_logs"].append(log_entry)

        # 保持日志数量在限制内
        if len(GLOBAL_PROGRESS["console_logs"]) > MAX_CONSOLE_LOGS:
            GLOBAL_PROGRESS["console_logs"] = GLOBAL_PROGRESS["console_logs"][-MAX_CONSOLE_LOGS:]

def update_global_progress(status=None, current=None, total=None, current_item=None, message=None, current_file=None, start_time=None, log_message=None):
    """
    更新全局进度（线程安全）
    """
    global GLOBAL_PROGRESS

    with PROGRESS_LOCK:
        if status is not None:
            GLOBAL_PROGRESS["status"] = status
        if current is not None:
            GLOBAL_PROGRESS["current"] = current
        if total is not None:
            GLOBAL_PROGRESS["total"] = total
        if current_item is not None:
            GLOBAL_PROGRESS["current_item"] = current_item
        if message is not None:
            GLOBAL_PROGRESS["message"] = message
        if current_file is not None:
            GLOBAL_PROGRESS["current_file"] = current_file
        if start_time is not None:
            GLOBAL_PROGRESS["start_time"] = start_time

        # 添加控制台日志
        if log_message is not None:
            add_console_log(log_message)

        # 计算百分比
        if GLOBAL_PROGRESS["total"] > 0:
            GLOBAL_PROGRESS["percentage"] = round((GLOBAL_PROGRESS["current"] / GLOBAL_PROGRESS["total"]) * 100, 1)
        else:
            GLOBAL_PROGRESS["percentage"] = 0

        # 计算预估剩余时间
        if GLOBAL_PROGRESS["start_time"] and GLOBAL_PROGRESS["current"] > 0:
            elapsed = time.time() - GLOBAL_PROGRESS["start_time"]
            if GLOBAL_PROGRESS["current"] < GLOBAL_PROGRESS["total"]:
                remaining_items = GLOBAL_PROGRESS["total"] - GLOBAL_PROGRESS["current"]
                avg_time_per_item = elapsed / GLOBAL_PROGRESS["current"]
                estimated_remaining_seconds = remaining_items * avg_time_per_item

                if estimated_remaining_seconds > 60:
                    GLOBAL_PROGRESS["estimated_remaining"] = f"{int(estimated_remaining_seconds // 60)}分{int(estimated_remaining_seconds % 60)}秒"
                else:
                    GLOBAL_PROGRESS["estimated_remaining"] = f"{int(estimated_remaining_seconds)}秒"
            else:
                GLOBAL_PROGRESS["estimated_remaining"] = "已完成"

        GLOBAL_PROGRESS["last_update"] = time.time()

        # 打印进度日志
        print(f"📊 进度更新: {GLOBAL_PROGRESS['current']}/{GLOBAL_PROGRESS['total']} ({GLOBAL_PROGRESS['percentage']}%) - {GLOBAL_PROGRESS['message']}")
        if current_item:
            print(f"   当前项目: {current_item}")

def reset_global_progress():
    """
    重置全局进度
    """
    global GLOBAL_PROGRESS

    with PROGRESS_LOCK:
        GLOBAL_PROGRESS.update({
            "status": "idle",
            "current": 0,
            "total": 0,
            "percentage": 0,
            "current_item": "",
            "message": "系统就绪",
            "start_time": None,
            "estimated_remaining": "未知",
            "processed_items": [],
            "current_file": "",
            "last_update": time.time(),
            "console_logs": []
        })

class API:
    def __init__(self):
        pass

    def get_users_data(self, page=1, page_size=200, search_params=None):
        """
        获取用户数据（分页+搜索）
        """
        try:
            print(f"=== 查询用户数据 ===")
            print(f"页码: {page}, 每页: {page_size}")
            if search_params:
                print(f"搜索条件: {search_params}")

            # 首先检查数据库和表是否存在
            import os
            db_path = 'system.db'
            if not os.path.exists(db_path):
                print(f"❌ 数据库文件不存在: {db_path}")
                return {
                    "success": False,
                    "message": "数据库文件不存在，请先上传Excel文件",
                    "data": [],
                    "total": 0
                }

            # 检查表是否存在
            conn = __import__('sqlite3').connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
            table_exists = cursor.fetchone() is not None

            if not table_exists:
                conn.close()
                print(f"❌ users表不存在")
                return {
                    "success": False,
                    "message": "users表不存在，请先上传Excel文件",
                    "data": [],
                    "total": 0
                }

            # 检查表中是否有数据
            cursor.execute("SELECT COUNT(*) FROM users")
            total_records = cursor.fetchone()[0]
            print(f"📊 数据库中总记录数: {total_records}")
            conn.close()

            # 构建搜索条件
            where_conditions = []
            params = []

            if search_params:
                if search_params.get('fileName'):
                    where_conditions.append("file_name LIKE ?")
                    params.append(f"%{search_params['fileName']}%")

                if search_params.get('description'):
                    where_conditions.append("intro LIKE ?")
                    params.append(f"%{search_params['description']}%")

                if search_params.get('phone'):
                    # 这里假设phone字段存在，如果没有可以搜索unique_id
                    where_conditions.append("unique_id LIKE ?")
                    params.append(f"%{search_params['phone']}%")

                if search_params.get('wechat'):
                    # 这里假设wechat字段存在，如果没有可以搜索username
                    where_conditions.append("username LIKE ?")
                    params.append(f"%{search_params['wechat']}%")

            # 构建WHERE子句
            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)

            # 查询总记录数
            conn = __import__('sqlite3').connect('system.db')
            conn.row_factory = __import__('sqlite3').Row
            cursor = conn.cursor()

            count_sql = f"SELECT COUNT(*) FROM users {where_clause}"
            cursor.execute(count_sql, params)
            total_count = cursor.fetchone()[0]
            print(f"符合条件的总记录数: {total_count}")

            # 计算偏移量
            offset = (page - 1) * page_size

            # 查询数据
            data_sql = f"""
                SELECT * FROM users
                {where_clause}
                ORDER BY create_time DESC
                LIMIT ? OFFSET ?
            """

            cursor.execute(data_sql, params + [page_size, offset])
            results = [dict(row) for row in cursor.fetchall()]
            conn.close()

            print(f"查询到 {len(results)} 条记录")
            for i, record in enumerate(results[:3], 1):
                print(f"  {i}. {record.get('username')} - {record.get('cmm_id')}")

            return {
                "success": True,
                "data": results,
                "total": total_count,
                "page": page,
                "page_size": page_size,
                "total_pages": (total_count + page_size - 1) // page_size,
                "search_params": search_params
            }

        except Exception as e:
            print(f"查询用户数据失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "message": f"查询失败: {str(e)}",
                "data": [],
                "total": 0
            }

    # 处理文件内容
    def process_file(self, file_data):
        """
        处理前端传来的文件数据，直接在内存中读取Excel内容
        :param file_data: 包含文件信息和内容的字典
        :return: 处理结果
        """
        import threading

        # 重置并初始化全局进度
        reset_global_progress()
        update_global_progress(
            status="processing",
            message="正在初始化文件处理...",
            log_message="🚀 开始处理Excel文件..."
        )

        def process_in_background():
            """后台处理函数"""
            try:
                self._do_file_processing(file_data)
            except Exception as e:
                print(f"❌ 后台处理异常: {str(e)}")
                import traceback
                traceback.print_exc()
                update_global_progress(
                    status="error",
                    message=f"处理失败: {str(e)}",
                    log_message=f"❌ 处理异常: {str(e)}"
                )

        # 启动后台线程
        thread = threading.Thread(target=process_in_background)
        thread.daemon = True
        thread.start()

        # 立即返回
        return {
            "success": True,
            "message": "文件处理已开始，请查看进度",
            "processing": True
        }

    def _do_file_processing(self, file_data):
        """
        实际的文件处理逻辑 - 简化版本
        """
        print("=" * 50)
        print("🚀 后台开始处理Excel文件...")

        try:
            # 验证数据
            if not file_data or 'name' not in file_data or 'content' not in file_data:
                update_global_progress(status="error", message="文件数据无效")
                return

            print(f"📄 处理文件: {file_data['name']}")
            update_global_progress(
                current_file=file_data['name'],
                message="正在解析Excel文件...",
                log_message=f"📄 开始处理: {file_data['name']}"
            )

            # 转换文件内容
            file_content = bytes(file_data['content'])
            from io import BytesIO
            file_stream = BytesIO(file_content)

            # 解析Excel
            import openpyxl
            workbook = openpyxl.load_workbook(file_stream)
            sheet = workbook.active

            print(f"✅ Excel解析成功: {sheet.max_row}行 x {sheet.max_column}列")

            # 读取表头
            headers = []
            for col in range(1, sheet.max_column + 1):
                cell_value = sheet.cell(row=1, column=col).value
                headers.append(str(cell_value) if cell_value else f"列{col}")

            # 扫描超链接
            hyperlinks_found = []
            total_rows = sheet.max_row - 1

            update_global_progress(
                total=total_rows,
                current=0,
                message=f"扫描 {total_rows} 行数据中的超链接...",
                log_message=f"🔍 开始扫描 {total_rows} 行数据"
            )

            for row in range(2, sheet.max_row + 1):
                # 更新进度
                if (row - 2) % 20 == 0:  # 每20行更新一次
                    update_global_progress(
                        current=row - 1,
                        message=f"扫描第 {row} 行..."
                    )

                for col in range(1, sheet.max_column + 1):
                    cell = sheet.cell(row=row, column=col)
                    if cell.hyperlink:
                        hyperlink_info = {
                            'row': row,
                            'col': col,
                            'column_name': headers[col-1] if col-1 < len(headers) else f"列{col}",
                            'cell_value': str(cell.value) if cell.value else "",
                            'hyperlink': cell.hyperlink.target if cell.hyperlink.target else str(cell.hyperlink)
                        }
                        hyperlinks_found.append(hyperlink_info)

            print(f"✅ 扫描完成，发现 {len(hyperlinks_found)} 个超链接")
            update_global_progress(
                current=total_rows,
                message=f"发现 {len(hyperlinks_found)} 个超链接",
                log_message=f"✅ 扫描完成：{len(hyperlinks_found)} 个超链接"
            )

            # 如果有超链接，开始处理
            if hyperlinks_found:
                self._process_hyperlinks(hyperlinks_found, file_data)
            else:
                update_global_progress(
                    status="completed",
                    message="处理完成，但未发现超链接",
                    log_message="⚠️ 未发现超链接，处理完成"
                )

        except Exception as e:
            error_msg = f"文件处理失败: {str(e)}"
            print(f"❌ {error_msg}")
            import traceback
            traceback.print_exc()
            update_global_progress(
                status="error",
                message=error_msg,
                log_message=f"❌ 处理失败: {str(e)}"
            )

    def _process_hyperlinks(self, hyperlinks_found, file_data):
        """
        处理超链接，获取达人信息
        """
        print(f"🔗 开始处理 {len(hyperlinks_found)} 个超链接")

        # 获取爬取配置
        crawl_config = file_data.get('crawlConfig', {})
        sleep_interval = crawl_config.get('sleepInterval', 3)

        update_global_progress(
            total=len(hyperlinks_found),
            current=0,
            message=f"开始获取 {len(hyperlinks_found)} 个达人信息...",
            log_message=f"🔗 发现 {len(hyperlinks_found)} 个超链接，开始获取达人信息"
        )

        # 处理每个超链接
        import time
        import re
        from datetime import datetime

        sqlite_data = []
        extracted_ids = []

        for i, link in enumerate(hyperlinks_found, 1):
            update_global_progress(
                current=i,
                current_item=f"获取达人: {link['cell_value']}",
                message=f"正在获取第 {i}/{len(hyperlinks_found)} 个达人信息",
                log_message=f"📡 [{i}/{len(hyperlinks_found)}] 获取达人: {link['cell_value']}"
            )

            # 提取ID
            url = link['hyperlink']
            match = re.search(r'/([a-zA-Z0-9_-]+)$', url)
            if match:
                user_id = match.group(1)
                extracted_ids.append(user_id)

                # 获取达人信息
                user_info = self.get_user_info_by_id(user_id)
                if user_info:
                    sqlite_data.append({
                        'user_id': user_id,
                        'nickname': user_info.get('nickname', ''),
                        'fans_count': user_info.get('fans_count', 0),
                        'aweme_count': user_info.get('aweme_count', 0),
                        'total_favorited': user_info.get('total_favorited', 0),
                        'signature': user_info.get('signature', ''),
                        'avatar_url': user_info.get('avatar_url', ''),
                        'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })

            # 休眠（除了最后一个）
            if i < len(hyperlinks_found):
                update_global_progress(
                    current_item="休眠中...",
                    log_message=f"⏱️ 休眠 {sleep_interval} 秒，避免请求过快"
                )
                time.sleep(sleep_interval)

            # 更新完成状态
            update_global_progress(
                current=i,
                current_item=f"已完成第{i}个达人",
                log_message=f"✅ 完成第 {i} 个达人信息获取"
            )

        # 保存到数据库
        if sqlite_data:
            try:
                success_count = self.batch_insert_users(sqlite_data)
                update_global_progress(
                    status="completed",
                    message=f"处理完成！成功获取 {success_count} 个达人信息",
                    log_message=f"🎉 Excel文件处理完成！成功获取 {success_count} 个达人信息"
                )
            except Exception as e:
                update_global_progress(
                    status="error",
                    message=f"数据保存失败: {str(e)}",
                    log_message=f"❌ 数据保存失败: {str(e)}"
                )
        else:
            update_global_progress(
                status="completed",
                message="处理完成，但未获取到有效数据",
                log_message="⚠️ 处理完成，但未获取到有效数据"
            )
                current_row_index = row - 2  # 当前处理的行索引（从0开始）

                # 每10行更新一次进度
                if current_row_index % 10 == 0 or current_row_index == total_rows - 1:
                    update_global_progress(
                        current=current_row_index + 1,
                        current_item=f"扫描第 {row} 行",
                        message=f"正在扫描数据 ({current_row_index + 1}/{total_rows})"
                    )

                row_data = []
                row_hyperlinks = []

                for col in range(1, sheet.max_column + 1):
                    cell = sheet.cell(row=row, column=col)
                    cell_value = str(cell.value) if cell.value else ""
                    row_data.append(cell_value)

                    # 检查是否有超链接
                    if cell.hyperlink:
                        hyperlink_info = {
                            'row': row,
                            'col': col,
                            'column_name': headers[col-1] if col-1 < len(headers) else f"列{col}",
                            'cell_value': cell_value,
                            'hyperlink': cell.hyperlink.target if cell.hyperlink.target else str(cell.hyperlink)
                        }
                        row_hyperlinks.append(hyperlink_info)
                        hyperlinks_found.append(hyperlink_info)

                        # 只在前几个超链接时打印详细信息
                        if len(hyperlinks_found) <= 5:
                            print(f"发现超链接 - 行{row}列{col}({headers[col-1] if col-1 < len(headers) else f'列{col}'}): {cell_value} -> {hyperlink_info['hyperlink']}")

                all_data.append(row_data)

                # 只打印前3行作为示例，减少日志输出
                if row <= 4:
                    print(f"第{row}行: {row_data}")
                    if row_hyperlinks:
                        for link in row_hyperlinks:
                            print(f"  └─ 超链接: {link['cell_value']} -> {link['hyperlink']}")

            print(f"\n数据读取完成！")
            print(f"总行数: {sheet.max_row - 1} 行数据")
            print(f"总列数: {sheet.max_column} 列")
            print(f"发现超链接: {len(hyperlinks_found)} 个")

            # 更新进度：数据扫描完成
            update_global_progress(
                current=total_rows,
                message=f"数据扫描完成，发现 {len(hyperlinks_found)} 个超链接",
                log_message=f"✅ 扫描完成：{total_rows} 行数据，{len(hyperlinks_found)} 个超链接"
            )

            # 打印所有超链接汇总
            if hyperlinks_found:
                print("\n=== 所有超链接汇总 ===")
                for i, link in enumerate(hyperlinks_found, 1):
                    print(f"{i}. 行{link['row']} {link['column_name']}: {link['cell_value']} -> {link['hyperlink']}")

                # 提取ID并组装SQLite数据
                print("\n=== 提取ID并获取真实数据 ===")
                import re
                from datetime import datetime

                sqlite_data = []
                extracted_ids = []
                insert_success = False
                insert_message = "未执行数据库插入"

                # 获取爬取配置
                crawl_config = file_data.get('crawlConfig', {})
                sleep_interval = crawl_config.get('sleepInterval', 3)  # 默认3秒
                print(f"⚙️ 爬取配置 - 休眠间隔: {sleep_interval}秒")

                # 更新进度：开始处理超链接
                import time  # 确保time模块可用
                update_global_progress(
                    total=len(hyperlinks_found),
                    current=0,
                    message=f"找到{len(hyperlinks_found)}个超链接，开始获取达人信息...",
                    start_time=time.time(),
                    log_message=f"🔗 发现 {len(hyperlinks_found)} 个超链接，开始获取达人信息"
                )

                # 获取最新token用于API调用
                print("🔑 获取最新token...")
                token = self.get_latest_token_from_db()
                if not token:
                    print("❌ 未找到有效token，将使用默认数据")
                else:
                    print(f"✅ 获取到token: {token[:20]}...")

                # 开始处理超链接
                total_count = len(hyperlinks_found)
                processed_count = 0

                for i, link in enumerate(hyperlinks_found, 1):
                    # 更新当前处理进度
                    update_global_progress(
                        current=i-1,
                        current_item=f"正在处理第{i}个超链接",
                        message=f"正在获取达人信息 ({i}/{len(hyperlinks_found)})"
                    )

                    # 提取authorDetail/后面的ID
                    url = link['hyperlink']
                    match = re.search(r'authorDetail/([^/?]+)', url)
                    if match:
                        author_id = match.group(1)
                        extracted_ids.append(author_id)

                        print(f"\n📡 [{i}/{len(hyperlinks_found)}] 获取达人信息: {author_id}")

                        # 更新进度：显示当前处理的达人ID
                        update_global_progress(
                            current_item=f"获取达人信息: {author_id}",
                            message=f"正在获取达人信息 ({i}/{len(hyperlinks_found)})",
                            log_message=f"📡 [{i}/{len(hyperlinks_found)}] 获取达人: {author_id}"
                        )

                        # 默认数据（如果API调用失败时使用）
                        default_intro = "专业达人，内容优质"
                        default_unique_id = f"user_{author_id[:8]}"

                        # 调用get_real_info获取真实数据
                        real_intro = default_intro
                        real_unique_id = default_unique_id
                        real_code = ''  # 默认联系方式为空

                        if token:
                            try:
                                from cmm import get_real_info, extract_contact_code
                                real_data = get_real_info(author_id, token)

                                if real_data and real_data.get('signature') and real_data.get('unique_id'):
                                    real_intro = real_data['signature']
                                    real_unique_id = real_data['unique_id']

                                    # 从signature中提取联系方式
                                    real_code = extract_contact_code(real_intro)

                                    print(f"✅ 获取成功 - 简介: {real_intro[:30]}... | 抖音ID: {real_unique_id}")
                                    if real_code:
                                        print(f"   📞 提取联系方式: {real_code}")
                                    else:
                                        print(f"   📞 未提取到联系方式")
                                else:
                                    print(f"⚠️ API返回数据不完整，使用默认数据")
                                    print(f"   返回数据: {str(real_data)[:100]}...")

                            except Exception as e:
                                print(f"❌ API调用失败: {str(e)[:50]}..., 使用默认数据")
                        else:
                            print(f"⚠️ 无token，使用默认数据")

                        data_row = {
                            'file_name': file_data['name'],
                            'username': link['cell_value'],
                            'intro': real_intro,
                            'unique_id': real_unique_id,
                            'cmm_id': author_id,
                            'code': real_code,
                            'create_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        }

                        sqlite_data.append(data_row)
                        processed_count += 1

                        print(f"📝 数据组装完成 - 用户: {link['cell_value']} | 抖音ID: {real_unique_id} | 联系方式: {real_code if real_code else '未提取'}")

                        # 更新进度
                        progress_msg = f"正在处理第{processed_count}个达人: {link['cell_value']}"
                        self.update_processing_progress(processed_count, total_count, progress_msg)

                        # 添加休眠间隔，避免请求过快
                        if i < len(hyperlinks_found):  # 最后一个不需要休眠
                            print(f"⏱️ 休眠{sleep_interval}秒，避免请求过快...")
                            update_global_progress(
                                current=i,
                                current_item=f"休眠{sleep_interval}秒...",
                                message=f"已处理 {i}/{len(hyperlinks_found)} 个达人，休眠中...",
                                log_message=f"⏱️ 休眠 {sleep_interval} 秒，避免请求过快"
                            )
                            time.sleep(sleep_interval)

                        # 更新进度：完成当前项目（包括最后一个）
                        update_global_progress(
                            current=i,
                            current_item=f"已完成第{i}个达人信息获取",
                            message=f"已处理 {i}/{len(hyperlinks_found)} 个达人",
                            log_message=f"✅ 完成第 {i} 个达人信息获取"
                        )

                print(f"\n提取到 {len(extracted_ids)} 个ID:")
                print(extracted_ids)

                # 生成批量插入SQL
                if sqlite_data:
                    print(f"\n=== 批量插入SQLite SQL语句 ===")
                    print("-- users 表结构")
                    print("CREATE TABLE IF NOT EXISTS users (")
                    print("    id INTEGER PRIMARY KEY AUTOINCREMENT,")
                    print("    file_name TEXT NOT NULL,  -- 原始文件名")
                    print("    username TEXT NOT NULL,   -- 用户昵称")
                    print("    intro TEXT,               -- 简介信息")
                    print("    unique_id TEXT,           -- 抖音ID")
                    print("    cmm_id TEXT,              -- 蝉妈妈ID")
                    print("    create_time TEXT NOT NULL -- 创建时间")
                    print(");")
                    print()

                    # 批量插入语句
                    print("-- 批量插入数据")
                    print("INSERT INTO users (file_name, username, intro, unique_id, cmm_id, code, create_time) VALUES")
                    values_list = []
                    for data in sqlite_data:
                        values = f"('{data['file_name']}', '{data['username']}', '{data['intro']}', '{data['unique_id']}', '{data['cmm_id']}', '{data['code']}', '{data['create_time']}')"
                        values_list.append(values)

                    print(",\n".join(values_list) + ";")

                    print(f"\n总共 {len(sqlite_data)} 条数据准备插入到 users 表")

                    # 详细数据预览
                    print(f"\n=== 数据详情预览 ===")
                    for i, data in enumerate(sqlite_data[:5], 1):  # 只显示前5条
                        print(f"{i}. 文件: {data['file_name']}")
                        print(f"   用户: {data['username']}")
                        print(f"   简介: {data['intro'][:50]}...")
                        print(f"   抖音ID: {data['unique_id']}")
                        print(f"   蝉妈妈ID: {data['cmm_id']}")
                        print(f"   联系方式: {data['code'] if data['code'] else '未提取'}")
                        print(f"   时间: {data['create_time']}")
                        print()

                    if len(sqlite_data) > 5:
                        print(f"... 还有 {len(sqlite_data) - 5} 条数据")

                    # 插入数据到数据库
                    print(f"\n=== 开始插入数据到数据库 ===")
                    try:
                        # 准备插入数据
                        field_names = ['file_name', 'username', 'intro', 'unique_id', 'cmm_id', 'code', 'create_time']
                        insert_data = []

                        for data in sqlite_data:
                            row_tuple = (
                                data['file_name'],
                                data['username'],
                                data['intro'],
                                data['unique_id'],
                                data['cmm_id'],
                                data['code'],
                                data['create_time']
                            )
                            insert_data.append(row_tuple)

                        print(f"准备插入 {len(insert_data)} 条数据到 users 表...")

                        # 执行批量插入
                        inserted_count = batch_insert(
                            db_path='system.db',
                            table_name='users',
                            field_names=field_names,
                            data=insert_data,
                            batch_size=50
                        )

                        if inserted_count > 0:
                            print(f"✅ 成功插入 {inserted_count} 条数据到 users 表")

                            # 验证插入结果
                            print(f"\n=== 验证数据库插入结果 ===")
                            verify_result = verify_insert_result()
                            print(f"数据库文件: {verify_result.get('db_path', 'unknown')}")
                            print(f"文件存在: {verify_result.get('file_exists', False)}")
                            print(f"表存在: {verify_result.get('table_exists', False)}")
                            print(f"总记录数: {verify_result.get('record_count', 0)}")

                            if verify_result.get('latest_records'):
                                print(f"最新5条记录:")
                                for i, record in enumerate(verify_result['latest_records'][:3], 1):
                                    print(f"  {i}. ID:{record.get('id')} 用户:{record.get('username')} 蝉妈妈ID:{record.get('cmm_id')}")

                            insert_success = True
                            insert_message = f"成功插入 {inserted_count} 条数据到数据库，当前总记录数: {verify_result.get('record_count', 0)}"
                        else:
                            print(f"❌ 数据插入失败")
                            insert_success = False
                            insert_message = "数据插入失败"

                    except Exception as insert_error:
                        print(f"❌ 数据库插入异常: {str(insert_error)}")
                        import traceback
                        traceback.print_exc()
                        insert_success = False
                        insert_message = f"数据库插入异常: {str(insert_error)}"

            else:
                print("未发现任何超链接")

            # 关闭工作簿
            workbook.close()

            print("=" * 50)
            print("Excel文件读取完成！")

            # 准备返回数据
            return_data = {
                "filename": file_data['name'],
                "sheet_names": sheet_names,
                "current_sheet": sheet.title,
                "max_row": sheet.max_row,
                "max_column": sheet.max_column,
                "headers": headers,
                "sample_data": all_data[:5],  # 前5行作为示例
                "total_rows": len(all_data),
                "hyperlinks_count": len(hyperlinks_found),
                "hyperlinks": hyperlinks_found
            }

            # 如果有超链接，添加提取的ID和SQLite数据
            if hyperlinks_found and 'extracted_ids' in locals():
                return_data.update({
                    "extracted_ids": extracted_ids,
                    "sqlite_data": sqlite_data,
                    "sqlite_ready_count": len(sqlite_data),
                    "db_insert_success": insert_success,
                    "db_insert_message": insert_message
                })

            # 更新进度：处理完成
            update_global_progress(
                status="completed",
                current=len(hyperlinks_found) if hyperlinks_found else 0,
                message="Excel文件处理完成！",
                current_item="处理完成",
                log_message="🎉 Excel文件处理完成！所有达人信息已获取"
            )

            return {
                "success": True,
                "message": "Excel文件读取成功",
                "data": return_data
            }

        except Exception as e:
            print(f"Excel文件处理错误: {str(e)}")
            import traceback
            traceback.print_exc()

            # 更新进度：处理失败
            update_global_progress(
                status="error",
                message=f"Excel文件处理失败: {str(e)}",
                current_item="处理失败",
                log_message=f"❌ 处理失败: {str(e)}"
            )

            return {
                "success": False,
                "message": f"Excel文件处理失败: {str(e)}",
                "error": str(e)
            }

    def api_login_cmm(self, username, password):
        """
        蝉妈妈登录API接口
        """
        try:
            print(f"=== 蝉妈妈登录 ===")
            print(f"用户名: {username}")

            # 验证输入
            if not username or not password:
                return {
                    "success": False,
                    "message": "用户名和密码不能为空",
                    "logged_in": False,
                    "token": ''
                }

            # 调用cmm.py的登录方法
            from cmm import login_cmm
            result = login_cmm(username, password)

            if result and result.get('data', {}).get('logged_in'):
                print("✅ 蝉妈妈登录成功")
                return {
                    "success": True,
                    "message": "登录成功",
                    "data": result.get('data', {}),
                    "logged_in": result.get('data', {}).get('logged_in', False),
                    "token": result.get('data', {}).get('token', '')
                }
            else:
                print("❌ 蝉妈妈登录失败")
                error_msg = "登录失败，请检查用户名和密码"
                if result and result.get('message'):
                    error_msg = result.get('message')

                return {
                    "success": False,
                    "message": error_msg,
                    "data": result,
                    "logged_in": False,
                    "token": ''
                }

        except Exception as e:
            print(f"❌ 蝉妈妈登录异常: {str(e)}")
            return {
                "success": False,
                "message": f"登录异常: {str(e)}",
                "logged_in": False,
                "token": ''
            }
        
    # 查询数据库的最新token
    def get_latest_token_from_db(self):
        """
        从数据库获取最新token
        """
        try:
            import sqlite3
            conn = sqlite3.connect('system.db')
            cursor = conn.cursor()

            cursor.execute("""
                SELECT token FROM tokens
                ORDER BY create_time DESC
                LIMIT 1
            """)
            result = cursor.fetchone()
            conn.close()

            if result:
                return result[0]
            else:
                return None
        except Exception as e:
            print(f"❌ 从数据库获取token失败: {str(e)}")
            return None



    def update_processing_progress(self, current=None, total=None, message=None):
        """
        更新处理进度（已重构为使用全局进度跟踪）
        """
        # 更新全局进度
        if current is not None or total is not None or message is not None:
            update_global_progress(current=current, total=total, message=message)

        # 返回当前全局进度状态
        return self.get_processing_status()

    def get_processing_status(self):
        """
        获取处理状态（用于前端轮询显示进度）
        返回全局进度跟踪的实时状态
        """
        global GLOBAL_PROGRESS

        with PROGRESS_LOCK:
            # 创建进度副本，避免并发修改
            progress_copy = GLOBAL_PROGRESS.copy()

            # 添加一些额外的状态信息
            progress_copy["timestamp"] = time.time()
            progress_copy["is_processing"] = progress_copy["status"] == "processing"
            progress_copy["is_completed"] = progress_copy["status"] == "completed"
            progress_copy["is_error"] = progress_copy["status"] == "error"
            progress_copy["is_idle"] = progress_copy["status"] == "idle"

            # 计算运行时间
            if progress_copy["start_time"]:
                elapsed = time.time() - progress_copy["start_time"]
                if elapsed > 60:
                    progress_copy["elapsed_time"] = f"{int(elapsed // 60)}分{int(elapsed % 60)}秒"
                else:
                    progress_copy["elapsed_time"] = f"{int(elapsed)}秒"
            else:
                progress_copy["elapsed_time"] = "0秒"

            # 只在调试模式下打印详细日志
            if progress_copy["status"] == "processing":
                print(f"📊 返回进度状态: {progress_copy['current']}/{progress_copy['total']} ({progress_copy['percentage']}%) - {progress_copy['message']}")

            return progress_copy

    def save_export_file(self, file_content, file_name, file_type='csv'):
        """
        保存导出文件到用户指定位置（使用pywebview文件对话框）
        """
        import os

        try:
            print(f"=== 保存导出文件 ===")
            print(f"文件名: {file_name}")
            print(f"文件类型: {file_type}")
            print(f"内容长度: {len(file_content)} 字符")

            # 尝试使用pywebview的文件对话框
            try:
                import webview

                # 设置文件类型过滤器
                if file_type.lower() == 'csv':
                    file_types = ('CSV文件 (*.csv)', '*.csv')
                elif file_type.lower() == 'xlsx':
                    file_types = ('Excel文件 (*.xlsx)', '*.xlsx')
                else:
                    file_types = ('所有文件 (*.*)', '*.*')

                # 使用pywebview的保存文件对话框
                file_path = webview.windows[0].create_file_dialog(
                    webview.SAVE_DIALOG,
                    directory=os.path.expanduser('~/Downloads'),  # 默认下载文件夹
                    save_filename=file_name,
                    file_types=(file_types,)
                )

                if not file_path:
                    print("用户取消了文件保存")
                    return {
                        "success": False,
                        "message": "用户取消了文件保存"
                    }

                # file_path可能是列表，取第一个
                if isinstance(file_path, (list, tuple)):
                    file_path = file_path[0] if file_path else None

                if not file_path:
                    return {
                        "success": False,
                        "message": "未选择保存路径"
                    }

                print(f"保存路径: {file_path}")

            except Exception as webview_error:
                print(f"⚠️ pywebview文件对话框失败: {str(webview_error)}")

                # 备用方案：直接保存到下载文件夹
                downloads_dir = os.path.expanduser('~/Downloads')
                if not os.path.exists(downloads_dir):
                    downloads_dir = os.path.expanduser('~')  # 用户主目录

                file_path = os.path.join(downloads_dir, file_name)
                print(f"使用默认路径: {file_path}")

            # 保存文件
            if file_type.lower() == 'csv':
                # CSV文件需要UTF-8编码和BOM
                with open(file_path, 'w', encoding='utf-8-sig', newline='') as f:
                    f.write(file_content)
            else:
                # 其他文件类型
                with open(file_path, 'w', encoding='utf-8', newline='') as f:
                    f.write(file_content)

            # 验证文件是否保存成功
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                print(f"✅ 文件保存成功")
                print(f"   路径: {file_path}")
                print(f"   大小: {file_size} 字节")

                return {
                    "success": True,
                    "message": f"文件已保存到: {file_path}",
                    "file_path": file_path,
                    "file_size": file_size
                }
            else:
                print(f"❌ 文件保存失败")
                return {
                    "success": False,
                    "message": "文件保存失败"
                }

        except Exception as e:
            print(f"❌ 保存文件异常: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "message": f"保存文件失败: {str(e)}"
            }

    def save_to_downloads(self, file_content, file_name, file_type='csv'):
        """
        直接保存文件到Downloads文件夹（最简单的方案）
        """
        import os

        try:
            print(f"=== 直接保存到Downloads文件夹 ===")
            print(f"文件名: {file_name}")
            print(f"文件类型: {file_type}")
            print(f"内容长度: {len(file_content)} 字符")

            # 获取Downloads文件夹路径
            downloads_dir = os.path.expanduser('~/Downloads')
            if not os.path.exists(downloads_dir):
                # 如果Downloads文件夹不存在，使用用户主目录
                downloads_dir = os.path.expanduser('~')
                print(f"Downloads文件夹不存在，使用主目录: {downloads_dir}")

            # 构建完整文件路径
            file_path = os.path.join(downloads_dir, file_name)

            # 如果文件已存在，添加序号
            base_name, ext = os.path.splitext(file_path)
            counter = 1
            while os.path.exists(file_path):
                file_path = f"{base_name}_{counter}{ext}"
                counter += 1

            print(f"保存路径: {file_path}")

            # 保存文件
            if file_type.lower() == 'csv':
                # CSV文件需要UTF-8编码和BOM
                with open(file_path, 'w', encoding='utf-8-sig', newline='') as f:
                    f.write(file_content)
            else:
                # 其他文件类型
                with open(file_path, 'w', encoding='utf-8', newline='') as f:
                    f.write(file_content)

            # 验证文件是否保存成功
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                print(f"✅ 文件保存成功")
                print(f"   路径: {file_path}")
                print(f"   大小: {file_size} 字节")

                return {
                    "success": True,
                    "message": f"文件已保存到Downloads文件夹: {os.path.basename(file_path)}",
                    "file_path": file_path,
                    "file_size": file_size
                }
            else:
                print(f"❌ 文件保存失败")
                return {
                    "success": False,
                    "message": "文件保存失败"
                }

        except Exception as e:
            print(f"❌ 保存文件异常: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "message": f"保存文件失败: {str(e)}"
            }

    def save_excel_file(self, base64_content, file_name):
        """
        保存Excel文件（从Base64内容，使用pywebview文件对话框）
        """
        import os
        import base64

        try:
            print(f"=== 保存Excel文件 ===")
            print(f"文件名: {file_name}")
            print(f"Base64内容长度: {len(base64_content)} 字符")

            # 尝试使用pywebview的文件对话框
            try:
                import webview

                # 使用pywebview的保存文件对话框
                file_path = webview.windows[0].create_file_dialog(
                    webview.SAVE_DIALOG,
                    directory=os.path.expanduser('~/Downloads'),  # 默认下载文件夹
                    save_filename=file_name,
                    file_types=(('Excel文件 (*.xlsx)', '*.xlsx'),)
                )

                if not file_path:
                    print("用户取消了Excel文件保存")
                    return {
                        "success": False,
                        "message": "用户取消了文件保存"
                    }

                # file_path可能是列表，取第一个
                if isinstance(file_path, (list, tuple)):
                    file_path = file_path[0] if file_path else None

                if not file_path:
                    return {
                        "success": False,
                        "message": "未选择保存路径"
                    }

                print(f"保存路径: {file_path}")

            except Exception as webview_error:
                print(f"⚠️ pywebview文件对话框失败: {str(webview_error)}")

                # 备用方案：直接保存到下载文件夹
                downloads_dir = os.path.expanduser('~/Downloads')
                if not os.path.exists(downloads_dir):
                    downloads_dir = os.path.expanduser('~')  # 用户主目录

                file_path = os.path.join(downloads_dir, file_name)
                print(f"使用默认路径: {file_path}")

            # 解码Base64并保存文件
            excel_data = base64.b64decode(base64_content)

            with open(file_path, 'wb') as f:
                f.write(excel_data)

            # 验证文件是否保存成功
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                print(f"✅ Excel文件保存成功")
                print(f"   路径: {file_path}")
                print(f"   大小: {file_size} 字节")

                return {
                    "success": True,
                    "message": f"Excel文件已保存到: {file_path}",
                    "file_path": file_path,
                    "file_size": file_size
                }
            else:
                print(f"❌ Excel文件保存失败")
                return {
                    "success": False,
                    "message": "Excel文件保存失败"
                }

        except Exception as e:
            print(f"❌ 保存Excel文件异常: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "message": f"保存Excel文件失败: {str(e)}"
            }

    def save_excel_to_downloads(self, base64_content, file_name):
        """
        直接保存Excel文件到Downloads文件夹
        """
        import os
        import base64

        try:
            print(f"=== 直接保存Excel到Downloads文件夹 ===")
            print(f"文件名: {file_name}")
            print(f"Base64内容长度: {len(base64_content)} 字符")

            # 获取Downloads文件夹路径
            downloads_dir = os.path.expanduser('~/Downloads')
            if not os.path.exists(downloads_dir):
                # 如果Downloads文件夹不存在，使用用户主目录
                downloads_dir = os.path.expanduser('~')
                print(f"Downloads文件夹不存在，使用主目录: {downloads_dir}")

            # 构建完整文件路径
            file_path = os.path.join(downloads_dir, file_name)

            # 如果文件已存在，添加序号
            base_name, ext = os.path.splitext(file_path)
            counter = 1
            while os.path.exists(file_path):
                file_path = f"{base_name}_{counter}{ext}"
                counter += 1

            print(f"保存路径: {file_path}")

            # 解码Base64并保存文件
            excel_data = base64.b64decode(base64_content)

            with open(file_path, 'wb') as f:
                f.write(excel_data)

            # 验证文件是否保存成功
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                print(f"✅ Excel文件保存成功")
                print(f"   路径: {file_path}")
                print(f"   大小: {file_size} 字节")

                return {
                    "success": True,
                    "message": f"Excel文件已保存到Downloads文件夹: {os.path.basename(file_path)}",
                    "file_path": file_path,
                    "file_size": file_size
                }
            else:
                print(f"❌ Excel文件保存失败")
                return {
                    "success": False,
                    "message": "Excel文件保存失败"
                }

        except Exception as e:
            print(f"❌ 保存Excel文件异常: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "message": f"保存Excel文件失败: {str(e)}"
            }
