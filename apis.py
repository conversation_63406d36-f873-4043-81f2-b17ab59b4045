# 交互文件
import openpyxl
import os
import shutil
from datetime import datetime

class API:
    def __init__(self):
        # 确保上传目录存在
        self.upload_base_dir = "datas/upload"
        if not os.path.exists(self.upload_base_dir):
            os.makedirs(self.upload_base_dir)

    def say_hello(self, val):
        print(val)
        return "Hello, World!"

    # 监听excel上传
    def handle_upload_excel(self, file_path):
        """
        处理Excel文件上传
        :param file_path: 上传的文件路径
        :return: 处理结果
        """
        try:
            print(f"开始处理文件: {file_path}")

            # 检查文件是否存在
            if not os.path.exists(file_path):
                return {
                    "success": False,
                    "message": f"文件不存在: {file_path}",
                    "error": "文件路径无效"
                }

            # 创建以日期命名的文件夹
            today = datetime.now().strftime("%Y-%m-%d")
            date_dir = os.path.join(self.upload_base_dir, today)
            if not os.path.exists(date_dir):
                os.makedirs(date_dir)
                print(f"创建目录: {date_dir}")

            # 获取原始文件名
            original_filename = os.path.basename(file_path)

            # 生成新的文件路径
            timestamp = datetime.now().strftime("%H%M%S")
            name, ext = os.path.splitext(original_filename)
            new_filename = f"{name}_{timestamp}{ext}"
            new_file_path = os.path.join(date_dir, new_filename)

            # 复制文件到目标位置
            shutil.copy2(file_path, new_file_path)
            print(f"文件已复制到: {new_file_path}")

            # 解析Excel文件
            excel_data = self.parse_excel(new_file_path, original_filename)

            return {
                "success": True,
                "message": "文件上传成功",
                "file_path": new_file_path,
                "original_filename": original_filename,
                "data": excel_data
            }

        except Exception as e:
            print(f"文件处理错误: {str(e)}")
            return {
                "success": False,
                "message": f"文件上传失败: {str(e)}",
                "error": str(e)
            }

    def parse_excel(self, file_path, original_filename):
        """
        解析Excel文件，提取联系人信息
        :param file_path: Excel文件路径
        :param original_filename: 原始文件名
        :return: 解析后的数据
        """
        try:
            workbook = openpyxl.load_workbook(file_path)
            sheet = workbook.active

            # 获取所有数据
            data = []
            headers = []

            # 读取表头
            for cell in sheet[1]:
                headers.append(cell.value if cell.value else "")

            # 读取数据行
            for row in sheet.iter_rows(min_row=2, values_only=True):
                if any(cell for cell in row):  # 跳过空行
                    row_data = {}
                    for col_num, cell_value in enumerate(row):
                        if col_num < len(headers):
                            row_data[headers[col_num]] = cell_value if cell_value else ""

                    # 尝试识别常见的联系人字段
                    contact_info = self.extract_contact_info(row_data, original_filename)
                    if contact_info:
                        data.append(contact_info)

            workbook.close()

            return {
                "total_rows": len(data),
                "headers": headers,
                "contacts": data[:100]  # 限制返回前100条数据
            }

        except Exception as e:
            return {
                "error": f"Excel解析失败: {str(e)}",
                "total_rows": 0,
                "headers": [],
                "contacts": []
            }

    def extract_contact_info(self, row_data, original_filename):
        """
        从行数据中提取联系人信息
        :param row_data: 行数据字典
        :param original_filename: 原始文件名
        :return: 标准化的联系人信息
        """
        contact = {
            "original_filename": original_filename,
            "description": "",
            "phone": "",
            "wechat": ""
        }

        # 常见的字段映射
        phone_fields = ["手机", "手机号", "电话", "联系电话", "phone", "mobile", "tel"]
        wechat_fields = ["微信", "微信号", "wechat", "wx"]
        desc_fields = ["简介", "描述", "备注", "说明", "description", "desc", "remark", "note"]

        # 提取手机号
        for field in phone_fields:
            for key, value in row_data.items():
                if field in str(key).lower() and value:
                    contact["phone"] = str(value).strip()
                    break
            if contact["phone"]:
                break

        # 提取微信号
        for field in wechat_fields:
            for key, value in row_data.items():
                if field in str(key).lower() and value:
                    contact["wechat"] = str(value).strip()
                    break
            if contact["wechat"]:
                break

        # 提取描述信息
        for field in desc_fields:
            for key, value in row_data.items():
                if field in str(key).lower() and value:
                    contact["description"] = str(value).strip()
                    break
            if contact["description"]:
                break

        # 如果没有找到描述，尝试用其他非空字段作为描述
        if not contact["description"]:
            for key, value in row_data.items():
                if value and key not in ["手机", "手机号", "电话", "微信", "微信号"]:
                    contact["description"] = f"{key}: {str(value).strip()}"
                    break

        # 只有当至少有一个有效字段时才返回
        if contact["phone"] or contact["wechat"] or contact["description"]:
            return contact

        return None

    def get_contact_data(self, page=1, page_size=5, search_params=None):
        """
        获取联系人数据（支持搜索和分页）
        :param page: 页码
        :param page_size: 每页数量
        :param search_params: 搜索参数
        :return: 分页数据
        """
        try:
            # 这里可以从数据库或文件中读取数据
            # 目前返回示例数据
            all_data = [
                {
                    "id": 1,
                    "original_filename": "contacts_2024_01.xlsx",
                    "description": "2024年1月份客户联系人信息表",
                    "phone": "13800138001",
                    "wechat": "user001"
                },
                {
                    "id": 2,
                    "original_filename": "leads_data.xlsx",
                    "description": "潜在客户信息收集表，包含详细的联系方式和需求描述",
                    "phone": "13800138002",
                    "wechat": "user002"
                }
            ]

            # 应用搜索过滤
            if search_params:
                filtered_data = []
                for item in all_data:
                    match = True
                    if search_params.get('fileName') and search_params['fileName'] not in item['original_filename']:
                        match = False
                    if search_params.get('description') and search_params['description'] not in item['description']:
                        match = False
                    if search_params.get('phone') and search_params['phone'] not in item['phone']:
                        match = False
                    if search_params.get('wechat') and search_params['wechat'] not in item['wechat']:
                        match = False

                    if match:
                        filtered_data.append(item)

                all_data = filtered_data

            # 计算分页
            total = len(all_data)
            start_index = (page - 1) * page_size
            end_index = start_index + page_size
            page_data = all_data[start_index:end_index]

            return {
                "success": True,
                "data": page_data,
                "total": total,
                "page": page,
                "page_size": page_size
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"获取数据失败: {str(e)}",
                "error": str(e)
            }
    






