import requests
base_url = "https://api-service.chanmama.com/v1/author/detail/info?author_id="
cookies='Hm_lvt_1f19c27e7e3e3255a5c79248a7f4bdf1=**********,**********; HMACCOUNT=****************; CMM_A_C_ID=544cb5af-6e10-11f0-abe4-72592890e22e; CMM_U_C_ID=73cf4b47-6e10-11f0-a25a-2276cded5ba4; LOGIN-TOKEN-FORSNS=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.PgN2VAsLyx-TrYjXx5qLPFMDNfTo0wVFxpiou5nshpM; frontend_canary1=always; Hm_lpvt_1f19c27e7e3e3255a5c79248a7f4bdf1=**********; Authorization-By-CAS=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************.xEg4_7PIvRobFBhPHT4g6-pk9ic2lzOJeUDeWOkggwA'
import json
headers = {
            'authority': 'api-service.chanmama.com',
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'cache-control': 'no-cache',
            'origin': 'https://www.chanmama.com',
            'pragma': 'no-cache',
            'referer': 'https://www.chanmama.com/',
            'sec-ch-ua': '"Not)A;Brand";v="24", "Chromium";v="116"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36',
            'x-client-hash': '4e75f486521cd9471142b5dd4ad628f72f616c41',
            'x-client-id': '407213695',
            'x-client-version': '1',
            'x-encrypt-version': '2',
            'x-platform-id': '10000',
            'cookie': cookies
        }
def get_real_info(id):
    url = base_url + id
    response = requests.get(url, headers=headers)
    #转换成json对象
    json_data = json.loads(json.dumps(response.json(),ensure_ascii=False,indent=2))
    return json_data


if __name__ == "__main__":
  
    print(get_real_info('pih7V4XhDA2Uu2f9oRa6fw'))
