import openpyxl


def process_excel_file(file_path):
    """处理Excel文件"""
    try:
        print(f"[get_chats.py] 收到文件: {file_path}")

        # 读取Excel文件
        workbook = openpyxl.load_workbook(file_path)
        sheet = workbook.active

        print(f"[get_chats.py] 文件读取成功")
        print(f"[get_chats.py] 工作表: {sheet.title}")
        print(f"[get_chats.py] 行数: {sheet.max_row}")
        print(f"[get_chats.py] 列数: {sheet.max_column}")

        # 读取所有数据
        data = []
        for row in range(1, sheet.max_row + 1):
            row_data = []
            for col in range(1, sheet.max_column + 1):
                cell_value = sheet.cell(row=row, column=col).value
                row_data.append(cell_value)
            data.append(row_data)
            print(f"[get_chats.py] 第{row}行: {row_data}")

        print(f"[get_chats.py] 处理完成，共{len(data)}行数据")

        return {
            "success": True,
            "data": data,
            "total_count": len(data)
        }

    except Exception as e:
        print(f"[get_chats.py] 处理失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }
