import requests
import hashlib
import time
base_url = "https://api-service.chanmama.com/v1/author/detail/info?author_id="
cookies='LOGIN-TOKEN-FORSNS=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcHBJZCI6MTAwMDAsImFwcFZlcnNpb24iOiIiLCJleHBpcmVfdGltZSI6MTc1NDUwNjgwMCwiaWF0IjoxNzUzOTY3NzM4LCJpZCI6MTQ2ODc2OTIsImtpZCI6IlVTRVItRVRMRjhTVEVKTkswLTRYWlhCOSIsInJrIjoiR0lsNmoifQ.PgN2VAsLyx-TrYjXx5qLPFMDNfTo0wVFxpiou5nshpM'
import json
headers = {
            'origin': 'https://www.chanmama.com',
            'referer': 'https://www.chanmama.com/',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36',
            'x-client-hash': '4e75f486521cd9471142b5dd4ad628f72f616c41',
            'x-client-id': '407213695',
            'x-client-version': '1',
            'x-encrypt-version': '2',
            'x-platform-id': '10000',
            'cookie': cookies
        }
def get_real_info(id):
    url = base_url + id
    response = requests.get(url, headers=headers)
    #转换成json对象
    json_data = json.loads(json.dumps(response.json(),ensure_ascii=False,indent=2))
    return json_data
# 登录禅妈妈接口
def login_cmm():
    headers = {}

# 将密码通过 md5 加密
    password = "weekseven"
    md5_hash = hashlib.md5()
    md5_hash.update(password.encode())
    hex_digest = md5_hash.hexdigest()

    json_data = {
        'from_platform': None,
        'appId': 10000,
        'timeStamp': int(time.time()),
        'username': '18775642907',
        'password': hex_digest
    }
    response = requests.post('https://api-service.chanmama.com/v1/access/token', headers=headers, json=json_data)
    print(response.json())


if __name__ == "__main__":
  
    # print(get_real_info('pih7V4XhDA2Uu2f9oRa6fw'))
    login_cmm()
