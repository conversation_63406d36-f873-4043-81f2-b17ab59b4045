#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试性能优化后的API设计
"""

import time
from apis import API
from cmm import get_token_from_db, get_real_info

def test_performance():
    """测试性能优化"""
    print("🧪 测试性能优化后的API设计...")
    
    # 1. 测试从数据库获取token
    print("\n1️⃣ 测试从数据库获取token...")
    start_time = time.time()
    token = get_token_from_db()
    db_time = time.time() - start_time
    
    if token:
        print(f"✅ 获取token成功: {token[:20]}...")
        print(f"⏱️ 数据库查询耗时: {db_time:.4f}秒")
    else:
        print("❌ 数据库中没有token，请先登录蝉妈妈")
        return False
    
    # 2. 测试多次API调用（模拟批量处理）
    print("\n2️⃣ 测试批量API调用性能...")
    test_ids = [
        'Te4oLu6PzddK8v0S_JURlE20CMuhagMW',
        'pih7V4XhDA2Uu2f9oRa6fw',
        'Te4oLu6PzddK8v0S_JURlE20CMuhagMW',  # 重复ID测试
    ]
    
    # 方式1：每次都查询数据库（旧方式）
    print("\n📊 方式1：每次查询数据库（模拟旧方式）")
    start_time = time.time()
    for i, user_id in enumerate(test_ids, 1):
        db_token = get_token_from_db()  # 每次都查询数据库
        print(f"   {i}. 查询数据库获取token: {db_token[:20] if db_token else 'None'}...")
        # 这里不实际调用API，只模拟查询数据库的开销
    old_way_time = time.time() - start_time
    print(f"⏱️ 旧方式总耗时: {old_way_time:.4f}秒")
    
    # 方式2：一次查询，多次使用（新方式）
    print("\n📊 方式2：一次查询，多次使用（新方式）")
    start_time = time.time()
    cached_token = get_token_from_db()  # 只查询一次
    for i, user_id in enumerate(test_ids, 1):
        print(f"   {i}. 使用缓存token: {cached_token[:20] if cached_token else 'None'}...")
        # 这里不实际调用API，只模拟使用缓存token
    new_way_time = time.time() - start_time
    print(f"⏱️ 新方式总耗时: {new_way_time:.4f}秒")
    
    # 性能对比
    if old_way_time > 0:
        improvement = ((old_way_time - new_way_time) / old_way_time) * 100
        print(f"\n🚀 性能提升: {improvement:.1f}%")
    
    return True

def test_api_integration():
    """测试API集成"""
    print("\n🧪 测试API集成...")
    
    # 创建API实例
    api = API()
    
    # 1. 测试获取token
    print("\n1️⃣ 测试API获取token...")
    token = api.get_latest_token_from_db()
    if token:
        print(f"✅ API获取token成功: {token[:20]}...")
    else:
        print("❌ API获取token失败")
        return False
    
    # 2. 测试获取达人信息API
    print("\n2️⃣ 测试获取达人信息API...")
    test_id = 'Te4oLu6PzddK8v0S_JURlE20CMuhagMW'
    
    try:
        result = api.api_get_real_info(test_id)
        
        if result.get('success'):
            print("✅ 获取达人信息成功")
            print(f"   消息: {result.get('message')}")
            print(f"   数据: {str(result.get('data', {}))[:100]}...")
        else:
            print("❌ 获取达人信息失败")
            print(f"   错误: {result.get('message')}")
        
    except Exception as e:
        print(f"❌ API调用异常: {str(e)}")
        return False
    
    return True

def test_direct_call():
    """测试直接调用cmm.py方法"""
    print("\n🧪 测试直接调用cmm.py方法...")
    
    # 获取token
    token = get_token_from_db()
    if not token:
        print("❌ 没有可用token")
        return False
    
    # 直接调用get_real_info
    test_id = 'Te4oLu6PzddK8v0S_JURlE20CMuhagMW'
    
    try:
        print(f"📡 调用 get_real_info('{test_id}', '{token[:20]}...')")
        result = get_real_info(test_id, token)
        
        if result:
            print("✅ 直接调用成功")
            print(f"   返回数据: {str(result)[:100]}...")
        else:
            print("❌ 直接调用失败")
        
    except Exception as e:
        print(f"❌ 直接调用异常: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == '--performance':
            test_performance()
        elif sys.argv[1] == '--api':
            test_api_integration()
        elif sys.argv[1] == '--direct':
            test_direct_call()
        else:
            print("用法: python test_performance.py [--performance|--api|--direct]")
    else:
        # 运行所有测试
        print("🧪 运行所有测试...")
        test_performance()
        test_api_integration()
        test_direct_call()
        print("\n✅ 所有测试完成！")
