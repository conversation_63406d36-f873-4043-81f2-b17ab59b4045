#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简单导出功能
"""

from apis import API
import os

def test_simple_downloads_save():
    """测试直接保存到Downloads文件夹"""
    print("🧪 测试直接保存到Downloads文件夹...")
    
    # 创建API实例
    api = API()
    
    # 模拟CSV内容
    csv_content = """ID,用户昵称,抖音ID,蝉妈妈ID,简介,联系方式,来源文件,创建时间
1,测试用户1,user1,cmm1,商务合作：contact1,contact1,test1.xlsx,2024-01-01 12:00:00
2,测试用户2,user2,cmm2,微信：wx123,wx123,test2.xlsx,2024-01-01 12:01:00
3,测试用户3,user3,cmm3,普通简介，没有联系方式,,test3.xlsx,2024-01-01 12:02:00"""
    
    file_name = "测试数据_简单导出.csv"
    
    print(f"📄 CSV内容长度: {len(csv_content)} 字符")
    print(f"📁 文件名: {file_name}")
    
    # 测试保存功能
    try:
        result = api.save_to_downloads(csv_content, file_name, 'csv')
        
        print(f"📊 保存结果:")
        print(f"   成功: {result.get('success', False)}")
        print(f"   消息: {result.get('message', '无')}")
        
        if result.get('success'):
            print(f"   文件路径: {result.get('file_path', '无')}")
            print(f"   文件大小: {result.get('file_size', 0)} 字节")
            
            # 验证文件内容
            file_path = result.get('file_path')
            if file_path and os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8-sig') as f:
                    saved_content = f.read()
                
                print(f"✅ 文件验证成功")
                print(f"   保存的内容长度: {len(saved_content)} 字符")
                print(f"   内容匹配: {saved_content.strip() == csv_content.strip()}")
                
                # 显示文件位置
                print(f"📂 文件已保存到: {file_path}")
                
                return True, file_path
            else:
                print(f"❌ 文件不存在: {file_path}")
                return False, None
        else:
            print(f"❌ 保存失败: {result.get('message')}")
            return False, None
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        return False, None

def test_downloads_folder():
    """测试Downloads文件夹路径"""
    print("\n🧪 测试Downloads文件夹路径...")
    
    downloads_dir = os.path.expanduser('~/Downloads')
    home_dir = os.path.expanduser('~')
    
    print(f"📂 用户主目录: {home_dir}")
    print(f"📂 Downloads目录: {downloads_dir}")
    print(f"📂 Downloads存在: {os.path.exists(downloads_dir)}")
    
    if os.path.exists(downloads_dir):
        print(f"✅ Downloads文件夹可用")
        
        # 检查写入权限
        try:
            test_file = os.path.join(downloads_dir, 'test_write_permission.txt')
            with open(test_file, 'w') as f:
                f.write('test')
            
            if os.path.exists(test_file):
                os.remove(test_file)
                print(f"✅ Downloads文件夹有写入权限")
                return True
            else:
                print(f"❌ Downloads文件夹写入测试失败")
                return False
                
        except Exception as e:
            print(f"❌ Downloads文件夹写入权限测试失败: {str(e)}")
            return False
    else:
        print(f"⚠️ Downloads文件夹不存在，将使用主目录")
        return True

def test_file_naming():
    """测试文件命名冲突处理"""
    print("\n🧪 测试文件命名冲突处理...")
    
    api = API()
    
    # 创建一个测试文件
    downloads_dir = os.path.expanduser('~/Downloads')
    if not os.path.exists(downloads_dir):
        downloads_dir = os.path.expanduser('~')
    
    test_file_name = "命名冲突测试.csv"
    test_file_path = os.path.join(downloads_dir, test_file_name)
    
    # 先创建一个同名文件
    try:
        with open(test_file_path, 'w', encoding='utf-8') as f:
            f.write("原始文件内容")
        
        print(f"📄 创建测试文件: {test_file_path}")
        
        # 现在尝试保存同名文件
        new_content = "新文件内容"
        result = api.save_to_downloads(new_content, test_file_name, 'csv')
        
        if result.get('success'):
            new_file_path = result.get('file_path')
            print(f"✅ 冲突处理成功")
            print(f"   原文件: {test_file_path}")
            print(f"   新文件: {new_file_path}")
            print(f"   文件名不同: {new_file_path != test_file_path}")
            
            # 清理测试文件
            if os.path.exists(test_file_path):
                os.remove(test_file_path)
            if os.path.exists(new_file_path):
                os.remove(new_file_path)
            
            return True
        else:
            print(f"❌ 冲突处理失败: {result.get('message')}")
            return False
            
    except Exception as e:
        print(f"❌ 文件命名测试异常: {str(e)}")
        return False
    finally:
        # 确保清理测试文件
        if os.path.exists(test_file_path):
            try:
                os.remove(test_file_path)
            except:
                pass

def show_usage_guide():
    """显示使用指南"""
    print("\n" + "="*60)
    print("📋 pywebview导出功能使用指南")
    print("="*60)
    
    print("\n🎯 解决方案:")
    print("由于pywebview环境限制浏览器文件下载，我们提供了多种导出方式：")
    
    print("\n📊 导出选项:")
    print("1. 🔧 导出CSV文件 - 使用pywebview文件对话框选择保存位置")
    print("2. 📈 导出Excel文件 - 使用pywebview文件对话框选择保存位置")
    print("3. ⚡ 快速保存CSV到Downloads - 直接保存到Downloads文件夹")
    print("4. ⚡ 快速保存Excel到Downloads - 直接保存到Downloads文件夹")
    
    print("\n🚀 推荐使用:")
    print("• 如果想选择保存位置：使用 '导出CSV文件' 或 '导出Excel文件'")
    print("• 如果想快速导出：使用 '快速保存到Downloads' 选项")
    
    print("\n📂 文件保存位置:")
    downloads_dir = os.path.expanduser('~/Downloads')
    if os.path.exists(downloads_dir):
        print(f"• Downloads文件夹: {downloads_dir}")
    else:
        home_dir = os.path.expanduser('~')
        print(f"• 用户主目录: {home_dir}")
    
    print("\n🔄 文件命名:")
    print("• 如果文件已存在，会自动添加序号 (例如: 用户数据_1.csv)")
    print("• 文件名包含时间戳，避免大部分冲突")
    
    print("\n💡 使用步骤:")
    print("1. 在主界面点击 '导出' 按钮")
    print("2. 从下拉菜单选择导出方式")
    print("3. 确认导出对话框")
    print("4. 等待文件生成和保存")
    print("5. 查看成功消息中的文件路径")

def run_all_tests():
    """运行所有测试"""
    print("🧪 测试简单导出功能")
    print("="*60)
    
    tests = [
        ("Downloads文件夹测试", test_downloads_folder),
        ("文件命名冲突测试", test_file_naming),
        ("简单保存功能测试", test_simple_downloads_save),
    ]
    
    results = []
    saved_file = None
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_name == "简单保存功能测试":
                result, file_path = test_func()
                if result:
                    saved_file = file_path
            else:
                result = test_func()
            results.append((test_name, result))
            print(f"{'✅' if result else '❌'} {test_name}: {'通过' if result else '失败'}")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {str(e)}")
            results.append((test_name, False))
    
    # 显示使用指南
    show_usage_guide()
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 测试总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {status} {test_name}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有测试通过！简单导出功能已就绪！")
        if saved_file:
            print(f"\n📁 测试文件已保存到: {saved_file}")
            print("   请检查文件内容是否正确")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    run_all_tests()
