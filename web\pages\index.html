<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后台管理系统</title>

    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Element Plus Icons -->
    <link rel="stylesheet" href="https://unpkg.com/@element-plus/icons-vue/dist/index.css">
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <style>
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
        }

        .fade-enter-active, .fade-leave-active {
            transition: opacity 0.3s ease;
        }

        .fade-enter-from, .fade-leave-to {
            opacity: 0;
        }
    </style>
</head>
<body>
    <div id="app">
        <el-container style="height: 100vh;">
            <!-- 左侧导航栏 -->
            <el-aside width="250px">
                <el-container direction="vertical" style="height: 100%;">
                    <!-- Logo 区域 -->
                    <el-header height="60px" style="background: var(--el-color-primary); color: white; display: flex; align-items: center; justify-content: center;">
                        <div style="text-align: center;">
                            <div style="font-size: 18px; font-weight: bold;">管理系统</div>
                            <div style="font-size: 12px; opacity: 0.8;">Admin Dashboard</div>
                        </div>
                    </el-header>

                    <!-- 导航菜单 -->
                    <el-main style="padding: 0;">
                        <el-menu
                            :default-active="activeMenu"
                            @select="handleMenuSelect"
                            unique-opened
                            style="border-right: none;"
                        >
                            <el-menu-item index="home">
                                <el-icon><House /></el-icon>
                                <span>首页</span>
                            </el-menu-item>

                            <el-sub-menu index="chanmama">
                                <template #title>
                                    <el-icon><DataAnalysis /></el-icon>
                                    <span>蝉妈妈</span>
                                </template>
                                <el-menu-item index="chanmama-talent">
                                    <el-icon><User /></el-icon>
                                    <span>达人数据提取</span>
                                </el-menu-item>
                            </el-sub-menu>
                        </el-menu>
                    </el-main>
                </el-container>
            </el-aside>

            <!-- 右侧主内容区 -->
            <el-container direction="vertical">
                <!-- 顶部标题栏 -->
                <el-header height="60px" style="background: var(--el-color-primary); color: white; display: flex; align-items: center; justify-content: space-between; padding: 0 20px;">
                    <div>
                        <h3 style="margin: 0;">{{ currentPageTitle }}</h3>
                        <small style="opacity: 0.8;">{{ currentPageSubtitle }}</small>
                    </div>
                    <div>
                        <el-button type="primary" :icon="Refresh" circle @click="refreshContent" plain></el-button>
                        <el-button type="primary" :icon="Setting" circle plain></el-button>
                    </div>
                </el-header>

                <!-- 主内容区 -->
                <el-main style="background: var(--el-bg-color-page);">
                    <transition name="fade" mode="out-in">
                        <div v-if="activeMenu === 'home'" key="home">
                            <!-- 首页内容 -->
                            <el-card shadow="never" style="margin-bottom: 20px;">
                                <div style="text-align: center; padding: 40px 0;">
                                    <el-icon size="64" color="var(--el-color-primary)"><Trophy /></el-icon>
                                    <h1 style="margin: 20px 0 10px 0; color: var(--el-text-color-primary);">欢迎使用管理系统</h1>
                                    <p style="color: var(--el-text-color-regular); font-size: 16px;">现代化的后台管理界面，让工作更高效</p>
                                </div>
                            </el-card>

                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <el-card shadow="hover">
                                        <el-statistic title="总用户数" :value="1234" suffix="人">
                                            <template #prefix>
                                                <el-icon style="color: var(--el-color-primary);"><User /></el-icon>
                                            </template>
                                        </el-statistic>
                                    </el-card>
                                </el-col>

                                <el-col :span="6">
                                    <el-card shadow="hover">
                                        <el-statistic title="今日访问" :value="567" suffix="次">
                                            <template #prefix>
                                                <el-icon style="color: var(--el-color-success);"><View /></el-icon>
                                            </template>
                                        </el-statistic>
                                    </el-card>
                                </el-col>

                                <el-col :span="6">
                                    <el-card shadow="hover">
                                        <el-statistic title="数据处理" :value="89" suffix="%">
                                            <template #prefix>
                                                <el-icon style="color: var(--el-color-warning);"><DataAnalysis /></el-icon>
                                            </template>
                                        </el-statistic>
                                    </el-card>
                                </el-col>

                                <el-col :span="6">
                                    <el-card shadow="hover">
                                        <el-statistic title="系统状态" value="正常">
                                            <template #prefix>
                                                <el-icon style="color: var(--el-color-success);"><CircleCheck /></el-icon>
                                            </template>
                                        </el-statistic>
                                    </el-card>
                                </el-col>
                            </el-row>
                        </div>

                        <div v-else-if="activeMenu === 'chanmama-talent'" key="talent">
                            <!-- 达人数据提取页面 -->
                            <el-card shadow="never" style="margin-bottom: 20px;">
                                <div style="text-align: center; padding: 40px 0;">
                                    <el-icon size="64" color="var(--el-color-primary)"><DataAnalysis /></el-icon>
                                    <h1 style="margin: 20px 0 10px 0; color: var(--el-text-color-primary);">达人数据提取</h1>
                                    <p style="color: var(--el-text-color-regular); font-size: 16px;">蝉妈妈平台达人数据分析工具</p>
                                </div>
                            </el-card>

                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-card shadow="hover">
                                        <template #header>
                                            <div style="display: flex; align-items: center;">
                                                <el-icon style="margin-right: 8px;"><Search /></el-icon>
                                                <span>数据查询</span>
                                            </div>
                                        </template>
                                        <el-form label-width="80px">
                                            <el-form-item label="达人ID">
                                                <el-input placeholder="请输入达人ID"></el-input>
                                            </el-form-item>
                                            <el-form-item label="时间范围">
                                                <el-date-picker
                                                    type="daterange"
                                                    range-separator="至"
                                                    start-placeholder="开始日期"
                                                    end-placeholder="结束日期"
                                                    style="width: 100%;"
                                                />
                                            </el-form-item>
                                            <el-form-item>
                                                <el-button type="primary" style="width: 100%;">开始提取</el-button>
                                            </el-form-item>
                                        </el-form>
                                    </el-card>
                                </el-col>
                                <el-col :span="12">
                                    <el-card shadow="hover">
                                        <template #header>
                                            <div style="display: flex; align-items: center;">
                                                <el-icon style="margin-right: 8px;"><Document /></el-icon>
                                                <span>提取结果</span>
                                            </div>
                                        </template>
                                        <el-empty description="暂无数据" :image-size="100"></el-empty>
                                    </el-card>
                                </el-col>
                            </el-row>
                        </div>
                    </transition>
                </el-main>
            </el-container>
        </el-container>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>

    <script>
        const { createApp, ref, computed } = Vue;
        const { ElMessage } = ElementPlus;

        const app = createApp({
            setup() {
                const activeMenu = ref('home');

                const currentPageTitle = computed(() => {
                    const titles = {
                        'home': '首页概览',
                        'chanmama-talent': '达人数据提取'
                    };
                    return titles[activeMenu.value] || '首页概览';
                });

                const currentPageSubtitle = computed(() => {
                    const subtitles = {
                        'home': '系统运行状态一览',
                        'chanmama-talent': '蝉妈妈平台数据分析'
                    };
                    return subtitles[activeMenu.value] || '系统运行状态一览';
                });

                const handleMenuSelect = (index) => {
                    activeMenu.value = index;
                    ElMessage.success(`切换到${currentPageTitle.value}`);
                };

                const refreshContent = () => {
                    ElMessage.info('刷新内容');
                };

                return {
                    activeMenu,
                    currentPageTitle,
                    currentPageSubtitle,
                    handleMenuSelect,
                    refreshContent
                };
            }
        });

        // 注册所有图标
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }

        app.use(ElementPlus).mount('#app');
    </script>
</body>
</html>