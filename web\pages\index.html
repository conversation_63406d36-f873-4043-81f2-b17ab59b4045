<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后台管理系统</title>

    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Element Plus Icons -->
    <link rel="stylesheet" href="https://unpkg.com/@element-plus/icons-vue/dist/index.css">
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <style>
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
        }

        .fade-enter-active, .fade-leave-active {
            transition: opacity 0.3s ease;
        }

        .fade-enter-from, .fade-leave-to {
            opacity: 0;
        }
    </style>
</head>
<body>
    <div id="app">
        <el-container style="height: 100vh;">
            <!-- 左侧导航栏 -->
            <el-aside width="250px" style="border-right: 1px solid var(--el-border-color);">
                <el-container direction="vertical" style="height: 100%;">
                    <!-- Logo 区域 -->
                    <el-header height="80px" style="display: flex; align-items: center; justify-content: center; border-bottom: 1px solid var(--el-border-color);">
                        <div style="text-align: center;">
                            <div style="font-size: 20px; font-weight: bold; color: var(--el-color-primary); margin-bottom: 4px;">管理系统</div>
                            <div style="font-size: 12px; color: var(--el-text-color-secondary);">Admin Dashboard</div>
                        </div>
                    </el-header>

                    <!-- 导航菜单 -->
                    <el-main style="padding: 0;">
                        <el-menu
                            :default-active="activeMenu"
                            @select="handleMenuSelect"
                            unique-opened
                            style="border-right: none;"
                        >
                            <el-menu-item index="home">
                                <el-icon><i-ep-house /></el-icon>
                                <span>首页</span>
                            </el-menu-item>

                            <el-sub-menu index="chanmama">
                                <template #title>
                                    <el-icon><i-ep-data-analysis /></el-icon>
                                    <span>蝉妈妈</span>
                                </template>
                                <el-menu-item index="chanmama-talent">
                                    <el-icon><i-ep-user /></el-icon>
                                    <span>达人数据提取</span>
                                </el-menu-item>
                            </el-sub-menu>
                        </el-menu>
                    </el-main>
                </el-container>
            </el-aside>

            <!-- 右侧主内容区 -->
            <el-container direction="vertical">
                <!-- 顶部标题栏 -->
                <el-header height="80px" style="display: flex; align-items: center; justify-content: space-between; padding: 0 30px; border-bottom: 1px solid var(--el-border-color);">
                    <div>
                        <h2 style="margin: 0; color: var(--el-text-color-primary);">{{ currentPageTitle }}</h2>
                        <p style="margin: 4px 0 0 0; color: var(--el-text-color-secondary); font-size: 14px;">{{ currentPageSubtitle }}</p>
                    </div>
                    <div>
                        <el-button :icon="RefreshIcon" circle @click="refreshContent"></el-button>
                        <el-button :icon="SettingIcon" circle></el-button>
                    </div>
                </el-header>

                <!-- 主内容区 -->
                <el-main style="background: var(--el-bg-color-page);">
                    <transition name="fade" mode="out-in">
                        <div v-if="activeMenu === 'home'" key="home">
                            <!-- 首页内容 -->
                            <el-card shadow="never" style="margin-bottom: 24px;">
                                <div style="text-align: center; padding: 60px 0;">
                                    <el-icon size="80" color="var(--el-color-primary)"><i-ep-trophy /></el-icon>
                                    <h1 style="margin: 24px 0 12px 0; color: var(--el-text-color-primary); font-size: 32px; font-weight: 600;">欢迎使用管理系统</h1>
                                    <p style="color: var(--el-text-color-regular); font-size: 18px; margin: 0;">现代化的后台管理界面，让工作更高效</p>
                                </div>
                            </el-card>

                            <el-row :gutter="24">
                                <el-col :span="6">
                                    <el-card shadow="hover" style="border-radius: 12px;">
                                        <el-statistic title="总用户数" :value="1234" suffix="人">
                                            <template #prefix>
                                                <el-icon size="24" style="color: var(--el-color-primary);"><i-ep-user /></el-icon>
                                            </template>
                                        </el-statistic>
                                    </el-card>
                                </el-col>

                                <el-col :span="6">
                                    <el-card shadow="hover" style="border-radius: 12px;">
                                        <el-statistic title="今日访问" :value="567" suffix="次">
                                            <template #prefix>
                                                <el-icon size="24" style="color: var(--el-color-success);"><i-ep-view /></el-icon>
                                            </template>
                                        </el-statistic>
                                    </el-card>
                                </el-col>

                                <el-col :span="6">
                                    <el-card shadow="hover" style="border-radius: 12px;">
                                        <el-statistic title="数据处理" :value="89" suffix="%">
                                            <template #prefix>
                                                <el-icon size="24" style="color: var(--el-color-warning);"><i-ep-data-analysis /></el-icon>
                                            </template>
                                        </el-statistic>
                                    </el-card>
                                </el-col>

                                <el-col :span="6">
                                    <el-card shadow="hover" style="border-radius: 12px;">
                                        <el-statistic title="系统状态" value="正常">
                                            <template #prefix>
                                                <el-icon size="24" style="color: var(--el-color-success);"><i-ep-circle-check /></el-icon>
                                            </template>
                                        </el-statistic>
                                    </el-card>
                                </el-col>
                            </el-row>
                        </div>

                        <div v-else-if="activeMenu === 'chanmama-talent'" key="talent">
                            <!-- 达人数据提取页面 -->
                            <el-card shadow="never" style="margin-bottom: 24px;">
                                <div style="text-align: center; padding: 60px 0;">
                                    <el-icon size="80" color="var(--el-color-primary)"><i-ep-data-analysis /></el-icon>
                                    <h1 style="margin: 24px 0 12px 0; color: var(--el-text-color-primary); font-size: 32px; font-weight: 600;">达人数据提取</h1>
                                    <p style="color: var(--el-text-color-regular); font-size: 18px; margin: 0;">蝉妈妈平台达人数据分析工具</p>
                                </div>
                            </el-card>

                            <el-row :gutter="24">
                                <el-col :span="12">
                                    <el-card shadow="hover" style="border-radius: 12px;">
                                        <template #header>
                                            <div style="display: flex; align-items: center;">
                                                <el-icon size="18" style="margin-right: 8px;"><i-ep-search /></el-icon>
                                                <span style="font-weight: 600;">数据查询</span>
                                            </div>
                                        </template>
                                        <el-form label-width="80px" style="padding: 20px 0;">
                                            <el-form-item label="达人ID">
                                                <el-input placeholder="请输入达人ID" size="large"></el-input>
                                            </el-form-item>
                                            <el-form-item label="时间范围">
                                                <el-date-picker
                                                    type="daterange"
                                                    range-separator="至"
                                                    start-placeholder="开始日期"
                                                    end-placeholder="结束日期"
                                                    style="width: 100%;"
                                                    size="large"
                                                />
                                            </el-form-item>
                                            <el-form-item>
                                                <el-button type="primary" size="large" style="width: 100%;">
                                                    <el-icon style="margin-right: 8px;"><i-ep-download /></el-icon>
                                                    开始提取
                                                </el-button>
                                            </el-form-item>
                                        </el-form>
                                    </el-card>
                                </el-col>
                                <el-col :span="12">
                                    <el-card shadow="hover" style="border-radius: 12px;">
                                        <template #header>
                                            <div style="display: flex; align-items: center;">
                                                <el-icon size="18" style="margin-right: 8px;"><i-ep-document /></el-icon>
                                                <span style="font-weight: 600;">提取结果</span>
                                            </div>
                                        </template>
                                        <div style="padding: 40px 0;">
                                            <el-empty description="暂无数据，请先进行数据提取" :image-size="120"></el-empty>
                                        </div>
                                    </el-card>
                                </el-col>
                            </el-row>
                        </div>
                    </transition>
                </el-main>
            </el-container>
        </el-container>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>

    <script>
        const { createApp, ref, computed } = Vue;
        const { ElMessage } = ElementPlus;

        const app = createApp({
            setup() {
                const activeMenu = ref('home');

                const currentPageTitle = computed(() => {
                    const titles = {
                        'home': '首页概览',
                        'chanmama-talent': '达人数据提取'
                    };
                    return titles[activeMenu.value] || '首页概览';
                });

                const currentPageSubtitle = computed(() => {
                    const subtitles = {
                        'home': '系统运行状态一览',
                        'chanmama-talent': '蝉妈妈平台数据分析'
                    };
                    return subtitles[activeMenu.value] || '系统运行状态一览';
                });

                const handleMenuSelect = (index) => {
                    activeMenu.value = index;
                };

                const refreshContent = () => {
                    ElMessage.info('刷新内容');
                };

                // 图标引用
                const RefreshIcon = 'Refresh';
                const SettingIcon = 'Setting';

                return {
                    activeMenu,
                    currentPageTitle,
                    currentPageSubtitle,
                    handleMenuSelect,
                    refreshContent,
                    RefreshIcon,
                    SettingIcon
                };
            }
        });

        // 注册所有图标
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }

        app.use(ElementPlus).mount('#app');
    </script>
</body>
</html>