<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后台管理系统</title>

    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Element Plus Icons -->
    <script src="//unpkg.com/@element-plus/icons-vue"></script>
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <style>
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .fade-enter-active, .fade-leave-active {
            transition: opacity 0.3s ease;
        }

        .fade-enter-from, .fade-leave-to {
            opacity: 0;
        }

        /* 统一的容器样式 */
        .el-container {
            height: 100vh;
            overflow: hidden;
        }

        /* 侧边栏精致样式 */
        .el-aside {
            background: linear-gradient(180deg, #1e293b 0%, #334155 100%);
            position: relative;
            overflow: hidden;
        }

        .el-aside::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.03)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.03)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.02)"/><circle cx="20" cy="80" r="0.5" fill="rgba(255,255,255,0.02)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        /* Logo区域精致样式 */
        .logo-header {
            background: transparent;
            color: #1e293b;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #3b82f6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 菜单精致样式 */
        .el-menu {
            background: transparent !important;
            border: none !important;
            padding: 16px 0;
        }

        .el-menu-item, .el-sub-menu__title {
            margin: 6px 16px;
            border-radius: 12px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            color: #1e293b;
            background: white;
            border: 1px solid #e2e8f0;
            position: relative;
            overflow: hidden;
        }

        .el-menu-item::before, .el-sub-menu__title::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s ease;
        }

        .el-menu-item:hover::before, .el-sub-menu__title:hover::before {
            left: 100%;
        }

        .el-menu-item:hover, .el-sub-menu__title:hover {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
            color: white !important;
            border-color: #3b82f6;
            transform: translateX(4px);
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
        }

        .el-menu-item.is-active {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
            color: white !important;
            font-weight: 700;
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.4);
            border: 1px solid #3b82f6;
        }

        .el-sub-menu.is-active .el-sub-menu__title {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
            color: white !important;
            font-weight: 700;
            border: 1px solid #3b82f6;
        }

        /* 顶部标题栏精致样式 */
        .header-container {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-bottom: 1px solid #e2e8f0;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            position: relative;
        }

        .header-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899, #f59e0b);
        }

        /* 按钮精致样式 */
        .header-btn {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid #e2e8f0;
            background: white;
            color: #64748b;
            position: relative;
            overflow: hidden;
        }

        .header-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
            transition: all 0.3s ease;
            transform: translate(-50%, -50%);
        }

        .header-btn:hover::before {
            width: 100px;
            height: 100px;
        }

        .header-btn:hover {
            background: #f8fafc;
            border-color: #3b82f6;
            color: #3b82f6;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
        }

        /* Loading样式 */
        .loading-overlay {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(2px);
        }

        /* iframe容器 */
        .iframe-container {
            background: #EFEDED;
            overflow: hidden;
        }

        .iframe-container iframe {
            border: none;
        }

        /* 主内容区域 */
        .el-main {
            padding: 0;
            overflow: hidden;
            background: #f9fafb;
        }

        /* 动画效果 */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div id="app">
        <el-container style="height: 100vh;">
            <!-- 左侧导航栏 -->
            <el-aside width="260px">
                <el-container direction="vertical" style="height: 100%;">
                    <!-- Logo 区域 -->
                    <el-header height="72px" class="logo-header">
                        <div class="logo-content">
                            <div class="logo-icon">
                                <el-icon size="20" color="white"><Monitor /></el-icon>
                            </div>
                            <div style="text-align: left;">
                                <div style="font-size: 18px; font-weight: 700; line-height: 1.2;">
                                    智能管理系统
                                </div>
                                <div style="font-size: 11px; opacity: 0.8; font-weight: 400;">
                                    Smart Admin Dashboard
                                </div>
                            </div>
                        </div>
                    </el-header>

                    <!-- 导航菜单 -->
                    <el-main style="padding: 0;">
                        <el-menu
                            :default-active="activeMenu"
                            @select="handleMenuSelect"
                            unique-opened
                            style="border-right: none;"
                        >
                            <el-menu-item index="home">
                                <el-icon><House /></el-icon>
                                <span>首页</span>
                            </el-menu-item>

                            <el-sub-menu index="chanmama">
                                <template #title>
                                    <el-icon><TrendCharts /></el-icon>
                                    <span>蝉妈妈</span>
                                </template>
                                <el-menu-item index="chanmama-talent">
                                    <el-icon><User /></el-icon>
                                    <span>达人数据提取</span>
                                </el-menu-item>
                            </el-sub-menu>
                        </el-menu>
                    </el-main>
                </el-container>
            </el-aside>

            <!-- 右侧主内容区 -->
            <el-container direction="vertical">
                <!-- 顶部标题栏 -->
                <el-header height="72px" class="header-container" style="display: flex; align-items: center; justify-content: space-between; padding: 0 32px;">
                    <div style="display: flex; align-items: center; gap: 16px;">
                        <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); border-radius: 12px; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);">
                            <el-icon size="20" color="white">
                                <House v-if="activeMenu === 'home'" />
                                <TrendCharts v-else-if="activeMenu === 'chanmama-talent'" />
                                <House v-else />
                            </el-icon>
                        </div>
                        <div>
                            <h2 style="margin: 0; color: #1e293b; font-size: 20px; font-weight: 700;">{{ currentPageTitle }}</h2>
                            <p style="margin: 2px 0 0 0; color: #64748b; font-size: 14px;">{{ currentPageSubtitle }}</p>
                        </div>
                    </div>
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <div style="display: flex; align-items: center; gap: 8px; padding: 8px 16px; background: rgba(59, 130, 246, 0.1); border-radius: 20px; margin-right: 8px;">
                            <div style="width: 8px; height: 8px; background: #10b981; border-radius: 50%; animation: pulse 2s infinite;"></div>
                            <span style="font-size: 12px; color: #059669; font-weight: 500;">系统正常</span>
                        </div>
                        <el-button class="header-btn" circle @click="refreshContent" :loading="pageLoading">
                            <el-icon><Refresh /></el-icon>
                        </el-button>
                        <el-button class="header-btn" circle>
                            <el-icon><Setting /></el-icon>
                        </el-button>
                        <el-button class="header-btn" circle>
                            <el-icon><Bell /></el-icon>
                        </el-button>
                        <el-dropdown>
                            <el-button class="header-btn" circle>
                                <el-icon><User /></el-icon>
                            </el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item>
                                        <el-icon style="margin-right: 8px;"><User /></el-icon>
                                        个人中心
                                    </el-dropdown-item>
                                    <el-dropdown-item>
                                        <el-icon style="margin-right: 8px;"><Setting /></el-icon>
                                        系统设置
                                    </el-dropdown-item>
                                    <el-dropdown-item divided>
                                        <el-icon style="margin-right: 8px;"><SwitchButton /></el-icon>
                                        退出登录
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </el-header>

                <!-- 主内容区 -->
                <el-main class="el-main">
                    <!-- Loading 遮罩 -->
                    <div v-if="pageLoading" class="loading-overlay" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; display: flex; flex-direction: column; align-items: center; justify-content: center; z-index: 1000;">
                        <el-loading-spinner size="large"></el-loading-spinner>
                        <p style="margin-top: 16px; color: #6b7280; font-size: 14px;">页面加载中...</p>
                    </div>

                    <transition name="fade" mode="out-in">
                        <div v-if="activeMenu === 'home'" key="home">
                            <div class="iframe-container" style="height: calc(100vh - 72px); width: 100%;">
                                <iframe
                                    src="home.html"
                                    style="width: 100%; height: 100%;"
                                    @load="onIframeLoad"
                                ></iframe>
                            </div>
                        </div>

                        <div v-else-if="activeMenu === 'chanmama-talent'" key="talent">
                            <div class="iframe-container" style="height: calc(100vh - 72px); width: 100%;">
                                <iframe
                                    src="talent.html"
                                    style="width: 100%; height: 100%;"
                                    @load="onIframeLoad"
                                ></iframe>
                            </div>
                        </div>
                    </transition>
                </el-main>
            </el-container>
        </el-container>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>

    <script>
        const { createApp, ref, computed } = Vue;
        const { ElMessage } = ElementPlus;

        const app = createApp({
            setup() {
                const activeMenu = ref('home');
                const pageLoading = ref(false);

                const currentPageTitle = computed(() => {
                    const titles = {
                        'home': '首页概览',
                        'chanmama-talent': '达人数据提取'
                    };
                    return titles[activeMenu.value] || '首页概览';
                });

                const currentPageSubtitle = computed(() => {
                    const subtitles = {
                        'home': '系统运行状态一览',
                        'chanmama-talent': '蝉妈妈平台数据分析'
                    };
                    return subtitles[activeMenu.value] || '系统运行状态一览';
                });

                const handleMenuSelect = (index) => {
                    pageLoading.value = true;
                    activeMenu.value = index;
                    // 模拟加载时间
                    setTimeout(() => {
                        pageLoading.value = false;
                    }, 300);
                };

                const refreshContent = () => {
                    pageLoading.value = true;
                    setTimeout(() => {
                        pageLoading.value = false;
                        ElMessage.success('刷新完成');
                    }, 500);
                };

                const onIframeLoad = () => {
                    pageLoading.value = false;
                };

                return {
                    activeMenu,
                    pageLoading,
                    currentPageTitle,
                    currentPageSubtitle,
                    handleMenuSelect,
                    refreshContent,
                    onIframeLoad
                };
            }
        });

        // 注册所有图标
            Object.keys(ElementPlusIconsVue).forEach(key => {
            app.component(key, ElementPlusIconsVue[key]);
        });

        app.use(ElementPlus).mount('#app');
    </script>
</body>
</html>