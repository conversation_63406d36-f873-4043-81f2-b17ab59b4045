<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后台管理系统</title>

    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Element Plus Icons -->
    <link rel="stylesheet" href="https://unpkg.com/@element-plus/icons-vue/dist/index.css">
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, <PERSON>bu<PERSON>u, <PERSON><PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .admin-container {
            display: flex;
            height: 100vh;
            overflow: hidden;
        }

        .sidebar {
            width: 250px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .logo-subtitle {
            font-size: 12px;
            opacity: 0.8;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            margin: 20px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .content-header {
            padding: 20px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .content-body {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
            background: #f8fafc;
        }

        .nav-menu {
            border: none !important;
            background: transparent !important;
        }

        .nav-menu .el-menu-item {
            margin: 5px 15px;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .nav-menu .el-menu-item:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            transform: translateX(5px);
        }

        .nav-menu .el-menu-item.is-active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
        }

        .nav-menu .el-sub-menu__title {
            margin: 5px 15px;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .nav-menu .el-sub-menu__title:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
        }

        .nav-menu .el-sub-menu.is-active .el-sub-menu__title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
        }

        .welcome-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .welcome-icon {
            font-size: 64px;
            color: #667eea;
            margin-bottom: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .fade-enter-active, .fade-leave-active {
            transition: opacity 0.3s ease;
        }

        .fade-enter-from, .fade-leave-to {
            opacity: 0;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="admin-container">
            <!-- 左侧导航栏 -->
            <div class="sidebar animate__animated animate__slideInLeft">
                <div class="sidebar-header">
                    <div class="logo">管理系统</div>
                    <div class="logo-subtitle">Admin Dashboard</div>
                </div>

                <el-menu
                    class="nav-menu"
                    :default-active="activeMenu"
                    @select="handleMenuSelect"
                    unique-opened
                >
                    <el-menu-item index="home">
                        <el-icon><House /></el-icon>
                        <span>首页</span>
                    </el-menu-item>

                    <el-sub-menu index="chanmama">
                        <template #title>
                            <el-icon><DataAnalysis /></el-icon>
                            <span>蝉妈妈</span>
                        </template>
                        <el-menu-item index="chanmama-talent">
                            <el-icon><User /></el-icon>
                            <span>达人数据提取</span>
                        </el-menu-item>
                    </el-sub-menu>
                </el-menu>
            </div>

            <!-- 右侧主内容区 -->
            <div class="main-content animate__animated animate__fadeIn">
                <div class="content-header">
                    <div>
                        <h2>{{ currentPageTitle }}</h2>
                        <p style="opacity: 0.8; margin-top: 5px;">{{ currentPageSubtitle }}</p>
                    </div>
                    <div>
                        <el-button type="primary" :icon="Refresh" circle @click="refreshContent"></el-button>
                        <el-button type="info" :icon="Setting" circle></el-button>
                    </div>
                </div>

                <div class="content-body">
                    <transition name="fade" mode="out-in">
                        <div v-if="activeMenu === 'home'" key="home">
                            <!-- 首页内容 -->
                            <div class="welcome-card animate__animated animate__fadeInUp">
                                <div class="welcome-icon">🎉</div>
                                <h1 style="color: #333; margin-bottom: 10px;">欢迎使用管理系统</h1>
                                <p style="color: #666; font-size: 16px;">现代化的后台管理界面，让工作更高效</p>
                            </div>

                            <div class="stats-grid">
                                <div class="stat-card animate__animated animate__fadeInUp" style="animation-delay: 0.1s;">
                                    <el-statistic title="总用户数" :value="1234" suffix="人">
                                        <template #prefix>
                                            <el-icon style="color: #409eff;"><User /></el-icon>
                                        </template>
                                    </el-statistic>
                                </div>

                                <div class="stat-card animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
                                    <el-statistic title="今日访问" :value="567" suffix="次">
                                        <template #prefix>
                                            <el-icon style="color: #67c23a;"><View /></el-icon>
                                        </template>
                                    </el-statistic>
                                </div>

                                <div class="stat-card animate__animated animate__fadeInUp" style="animation-delay: 0.3s;">
                                    <el-statistic title="数据处理" :value="89" suffix="%">
                                        <template #prefix>
                                            <el-icon style="color: #e6a23c;"><DataAnalysis /></el-icon>
                                        </template>
                                    </el-statistic>
                                </div>

                                <div class="stat-card animate__animated animate__fadeInUp" style="animation-delay: 0.4s;">
                                    <el-statistic title="系统状态" value="正常">
                                        <template #prefix>
                                            <el-icon style="color: #f56c6c;"><CircleCheck /></el-icon>
                                        </template>
                                    </el-statistic>
                                </div>
                            </div>
                        </div>

                        <div v-else-if="activeMenu === 'chanmama-talent'" key="talent">
                            <!-- 达人数据提取页面 -->
                            <div class="welcome-card animate__animated animate__fadeInUp">
                                <div class="welcome-icon">📊</div>
                                <h1 style="color: #333; margin-bottom: 10px;">达人数据提取</h1>
                                <p style="color: #666; font-size: 16px;">蝉妈妈平台达人数据分析工具</p>

                                <div style="margin-top: 30px;">
                                    <el-row :gutter="20">
                                        <el-col :span="12">
                                            <el-card shadow="hover">
                                                <template #header>
                                                    <div style="display: flex; align-items: center;">
                                                        <el-icon style="margin-right: 8px;"><Search /></el-icon>
                                                        <span>数据查询</span>
                                                    </div>
                                                </template>
                                                <el-form label-width="80px">
                                                    <el-form-item label="达人ID">
                                                        <el-input placeholder="请输入达人ID"></el-input>
                                                    </el-form-item>
                                                    <el-form-item label="时间范围">
                                                        <el-date-picker
                                                            type="daterange"
                                                            range-separator="至"
                                                            start-placeholder="开始日期"
                                                            end-placeholder="结束日期"
                                                            style="width: 100%;"
                                                        />
                                                    </el-form-item>
                                                    <el-form-item>
                                                        <el-button type="primary" style="width: 100%;">开始提取</el-button>
                                                    </el-form-item>
                                                </el-form>
                                            </el-card>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-card shadow="hover">
                                                <template #header>
                                                    <div style="display: flex; align-items: center;">
                                                        <el-icon style="margin-right: 8px;"><Document /></el-icon>
                                                        <span>提取结果</span>
                                                    </div>
                                                </template>
                                                <el-empty description="暂无数据" :image-size="100"></el-empty>
                                            </el-card>
                                        </el-col>
                                    </el-row>
                                </div>
                            </div>
                        </div>
                    </transition>
                </div>
            </div>
        </div>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>

    <script>
        const { createApp, ref, computed } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            setup() {
                const activeMenu = ref('home');

                const currentPageTitle = computed(() => {
                    const titles = {
                        'home': '首页概览',
                        'chanmama-talent': '达人数据提取'
                    };
                    return titles[activeMenu.value] || '首页概览';
                });

                const currentPageSubtitle = computed(() => {
                    const subtitles = {
                        'home': '系统运行状态一览',
                        'chanmama-talent': '蝉妈妈平台数据分析'
                    };
                    return subtitles[activeMenu.value] || '系统运行状态一览';
                });

                const handleMenuSelect = (index) => {
                    activeMenu.value = index;
                    ElMessage.success(`切换到${currentPageTitle.value}`);
                };

                const refreshContent = () => {
                    ElMessage.info('刷新内容');
                };

                return {
                    activeMenu,
                    currentPageTitle,
                    currentPageSubtitle,
                    handleMenuSelect,
                    refreshContent
                };
            }
        })
        .use(ElementPlus)
        .mount('#app');

        // 注册所有图标
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }
    </script>
</body>
</html>