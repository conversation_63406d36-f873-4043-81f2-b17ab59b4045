<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后台管理系统</title>

    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Element Plus Icons -->
    <link rel="stylesheet" href="https://unpkg.com/@element-plus/icons-vue/dist/index.css">
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <style>
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
        }

        .fade-enter-active, .fade-leave-active {
            transition: opacity 0.3s ease;
        }

        .fade-enter-from, .fade-leave-to {
            opacity: 0;
        }
    </style>
</head>
<body>
    <div id="app">
        <el-container style="height: 100vh;">
            <!-- 左侧导航栏 -->
            <el-aside width="250px" style="border-right: 1px solid var(--el-border-color);">
                <el-container direction="vertical" style="height: 100%;">
                    <!-- Logo 区域 -->
                    <el-header height="80px" style="display: flex; align-items: center; justify-content: center; border-bottom: 1px solid var(--el-border-color); background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-light-3) 100%);">
                        <div style="text-align: center;">
                            <div style="font-size: 20px; font-weight: bold; color: white; margin-bottom: 4px;">🚀 管理系统</div>
                            <div style="font-size: 12px; color: rgba(255,255,255,0.8);">Admin Dashboard</div>
                        </div>
                    </el-header>

                    <!-- 导航菜单 -->
                    <el-main style="padding: 0;">
                        <el-menu
                            :default-active="activeMenu"
                            @select="handleMenuSelect"
                            unique-opened
                            style="border-right: none;"
                        >
                            <el-menu-item index="home">
                                <el-icon><House /></el-icon>
                                <span>首页</span>
                            </el-menu-item>

                            <el-sub-menu index="chanmama">
                                <template #title>
                                    <el-icon><DataAnalysis /></el-icon>
                                    <span>蝉妈妈</span>
                                </template>
                                <el-menu-item index="chanmama-talent">
                                    <el-icon><User /></el-icon>
                                    <span>达人数据提取</span>
                                </el-menu-item>
                            </el-sub-menu>
                        </el-menu>
                    </el-main>
                </el-container>
            </el-aside>

            <!-- 右侧主内容区 -->
            <el-container direction="vertical">
                <!-- 顶部欢迎面板 -->
                <el-header height="120px" style="background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-light-3) 100%); color: white; display: flex; align-items: center; justify-content: space-between; padding: 0 40px;">
                    <div style="display: flex; align-items: center;">
                        <div style="margin-right: 20px;">
                            <el-avatar :size="60" style="background: rgba(255,255,255,0.2);">
                                <el-icon size="30"><User /></el-icon>
                            </el-avatar>
                        </div>
                        <div>
                            <h1 style="margin: 0; font-size: 28px; font-weight: 600;">{{ getGreeting() }}</h1>
                            <p style="margin: 8px 0 0 0; font-size: 16px; opacity: 0.9;">{{ currentPageTitle }} - {{ currentPageSubtitle }}</p>
                            <div style="margin-top: 8px; display: flex; align-items: center;">
                                <el-tag type="success" size="small" style="margin-right: 8px;">在线</el-tag>
                                <span style="font-size: 14px; opacity: 0.8;">{{ getCurrentTime() }}</span>
                            </div>
                        </div>
                    </div>
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <el-button type="primary" :icon="Refresh" circle @click="refreshContent" size="large" plain></el-button>
                        <el-button type="primary" :icon="Setting" circle size="large" plain></el-button>
                        <el-button type="primary" :icon="Bell" circle size="large" plain></el-button>
                    </div>
                </el-header>

                <!-- 主内容区 -->
                <el-main style="background: var(--el-bg-color-page); padding: 0;">
                    <transition name="fade" mode="out-in">
                        <div v-if="activeMenu === 'home'" key="home" class="animate__animated animate__fadeIn">
                            <div class="iframe-container" style="height: calc(100vh - 120px); width: 100%;">
                                <iframe src="home.html" style="width: 100%; height: 100%; border: none;"></iframe>
                            </div>
                        </div>

                        <div v-else-if="activeMenu === 'chanmama-talent'" key="talent" class="animate__animated animate__fadeIn">
                            <div class="iframe-container" style="height: calc(100vh - 120px); width: 100%;">
                                <iframe src="talent.html" style="width: 100%; height: 100%; border: none;"></iframe>
                            </div>
                        </div>
                    </transition>
                </el-main>
            </el-container>
        </el-container>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>

    <script>
        const { createApp, ref, computed } = Vue;
        const { ElMessage } = ElementPlus;

        const app = createApp({
            setup() {
                const activeMenu = ref('home');

                const currentPageTitle = computed(() => {
                    const titles = {
                        'home': '首页概览',
                        'chanmama-talent': '达人数据提取'
                    };
                    return titles[activeMenu.value] || '首页概览';
                });

                const currentPageSubtitle = computed(() => {
                    const subtitles = {
                        'home': '系统运行状态一览',
                        'chanmama-talent': '蝉妈妈平台数据分析'
                    };
                    return subtitles[activeMenu.value] || '系统运行状态一览';
                });

                const handleMenuSelect = (index) => {
                    activeMenu.value = index;
                };

                const refreshContent = () => {
                    ElMessage.info('刷新内容');
                };

                const getGreeting = () => {
                    const hour = new Date().getHours();
                    if (hour < 12) return '早上好！';
                    if (hour < 18) return '下午好！';
                    return '晚上好！';
                };

                const getCurrentTime = () => {
                    return new Date().toLocaleString('zh-CN');
                };

                return {
                    activeMenu,
                    currentPageTitle,
                    currentPageSubtitle,
                    handleMenuSelect,
                    refreshContent,
                    getGreeting,
                    getCurrentTime
                };
            }
        });

        // 注册所有图标
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }

        app.use(ElementPlus).mount('#app');
    </script>
</body>
</html>