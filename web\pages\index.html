<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后台管理系统</title>

    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Element Plus Icons -->
    <script src="//unpkg.com/@element-plus/icons-vue"></script>
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <style>
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        }

        .fade-enter-active, .fade-leave-active {
            transition: opacity 0.3s ease;
        }

        .fade-enter-from, .fade-leave-to {
            opacity: 0;
        }

        /* 侧边栏美化 */
        .el-aside {
            background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
            box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
        }

        /* Logo区域美化 */
        .logo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .logo-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: logoShine 3s ease-in-out infinite;
        }

        @keyframes logoShine {
            0%, 100% { transform: rotate(0deg); }
            50% { transform: rotate(180deg); }
        }

        /* 菜单美化 */
        .el-menu {
            background: transparent !important;
            border: none !important;
        }

        .el-menu-item, .el-sub-menu__title {
            margin: 8px 16px;
            border-radius: 12px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .el-menu-item:hover, .el-sub-menu__title:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            transform: translateX(8px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .el-menu-item.is-active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .el-sub-menu.is-active .el-sub-menu__title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
        }

        /* 顶部标题栏美化 */
        .header-container {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        /* 按钮美化 */
        .header-btn {
            transition: all 0.3s ease;
        }

        .header-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* Loading动画美化 */
        .loading-overlay {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(4px);
        }

        /* iframe容器美化 */
        .iframe-container {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            margin: 16px;
            background: white;
        }

        .iframe-container iframe {
            border-radius: 12px;
        }
    </style>
</head>
<body>
    <div id="app">
        <el-container style="height: 100vh;">
            <!-- 左侧导航栏 -->
            <el-aside width="260px">
                <el-container direction="vertical" style="height: 100%;">
                    <!-- Logo 区域 -->
                    <el-header height="70px" class="logo-header" style="display: flex; align-items: center; justify-content: center; position: relative; z-index: 1;">
                        <div style="text-align: center; position: relative; z-index: 2;">
                            <div style="font-size: 20px; font-weight: 700; margin-bottom: 4px; display: flex; align-items: center; justify-content: center;">
                                <span style="margin-right: 8px;">🎯</span>
                                <span>智能管理系统</span>
                            </div>
                            <div style="font-size: 12px; opacity: 0.9; font-weight: 500;">Smart Admin Dashboard</div>
                        </div>
                    </el-header>

                    <!-- 导航菜单 -->
                    <el-main style="padding: 0;">
                        <el-menu
                            :default-active="activeMenu"
                            @select="handleMenuSelect"
                            unique-opened
                            style="border-right: none;"
                        >
                            <el-menu-item index="home">
                                <el-icon><House /></el-icon>
                                <span>首页</span>
                            </el-menu-item>

                            <el-sub-menu index="chanmama">
                                <template #title>
                                    <el-icon><TrendCharts /></el-icon>
                                    <span>蝉妈妈</span>
                                </template>
                                <el-menu-item index="chanmama-talent">
                                    <el-icon><User /></el-icon>
                                    <span>达人数据提取</span>
                                </el-menu-item>
                            </el-sub-menu>
                        </el-menu>
                    </el-main>
                </el-container>
            </el-aside>

            <!-- 右侧主内容区 -->
            <el-container direction="vertical">
                <!-- 顶部标题栏 -->
                <el-header height="70px" class="header-container" style="display: flex; align-items: center; justify-content: space-between; padding: 0 32px;">
                    <div style="display: flex; align-items: center;">
                        <div style="margin-right: 16px; padding: 8px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 12px;">
                            <el-icon size="24" color="white"><Monitor /></el-icon>
                        </div>
                        <div>
                            <h2 style="margin: 0; color: var(--el-text-color-primary); font-size: 20px; font-weight: 600;">{{ currentPageTitle }}</h2>
                            <p style="margin: 4px 0 0 0; color: var(--el-text-color-secondary); font-size: 14px;">{{ currentPageSubtitle }}</p>
                        </div>
                    </div>
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <el-button class="header-btn" type="primary" circle @click="refreshContent" :loading="pageLoading">
                            <el-icon><Refresh /></el-icon>
                        </el-button>
                        <el-button class="header-btn" circle>
                            <el-icon><Setting /></el-icon>
                        </el-button>
                        <el-button class="header-btn" circle>
                            <el-icon><Bell /></el-icon>
                        </el-button>
                        <el-dropdown>
                            <el-button class="header-btn" circle>
                                <el-icon><User /></el-icon>
                            </el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item>个人中心</el-dropdown-item>
                                    <el-dropdown-item>系统设置</el-dropdown-item>
                                    <el-dropdown-item divided>退出登录</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </el-header>

                <!-- 主内容区 -->
                <el-main style="background: #f8fafc; padding: 0; position: relative;">
                    <!-- Loading 遮罩 -->
                    <div v-if="pageLoading" class="loading-overlay" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; display: flex; flex-direction: column; align-items: center; justify-content: center; z-index: 1000;">
                        <el-loading-spinner size="large"></el-loading-spinner>
                        <p style="margin-top: 16px; color: var(--el-text-color-regular); font-size: 14px;">页面加载中...</p>
                    </div>

                    <transition name="fade" mode="out-in">
                        <div v-if="activeMenu === 'home'" key="home">
                            <div class="iframe-container" style="height: calc(100vh - 86px);">
                                <iframe
                                    src="home.html"
                                    style="width: 100%; height: 100%; border: none;"
                                    @load="onIframeLoad"
                                ></iframe>
                            </div>
                        </div>

                        <div v-else-if="activeMenu === 'chanmama-talent'" key="talent">
                            <div class="iframe-container" style="height: calc(100vh - 86px);">
                                <iframe
                                    src="talent.html"
                                    style="width: 100%; height: 100%; border: none;"
                                    @load="onIframeLoad"
                                ></iframe>
                            </div>
                        </div>
                    </transition>
                </el-main>
            </el-container>
        </el-container>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>

    <script>
        const { createApp, ref, computed } = Vue;
        const { ElMessage } = ElementPlus;

        const app = createApp({
            setup() {
                const activeMenu = ref('home');
                const pageLoading = ref(false);

                const currentPageTitle = computed(() => {
                    const titles = {
                        'home': '首页概览',
                        'chanmama-talent': '达人数据提取'
                    };
                    return titles[activeMenu.value] || '首页概览';
                });

                const currentPageSubtitle = computed(() => {
                    const subtitles = {
                        'home': '系统运行状态一览',
                        'chanmama-talent': '蝉妈妈平台数据分析'
                    };
                    return subtitles[activeMenu.value] || '系统运行状态一览';
                });

                const handleMenuSelect = (index) => {
                    pageLoading.value = true;
                    activeMenu.value = index;
                    // 模拟加载时间
                    setTimeout(() => {
                        pageLoading.value = false;
                    }, 300);
                };

                const refreshContent = () => {
                    pageLoading.value = true;
                    setTimeout(() => {
                        pageLoading.value = false;
                        ElMessage.success('刷新完成');
                    }, 500);
                };

                const onIframeLoad = () => {
                    pageLoading.value = false;
                };

                return {
                    activeMenu,
                    pageLoading,
                    currentPageTitle,
                    currentPageSubtitle,
                    handleMenuSelect,
                    refreshContent,
                    onIframeLoad
                };
            }
        });

        // 注册所有图标
            Object.keys(ElementPlusIconsVue).forEach(key => {
            app.component(key, ElementPlusIconsVue[key]);
        });

        app.use(ElementPlus).mount('#app');
    </script>
</body>
</html>