<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="/web/css/style.css">
</head>
<body>
    <button onclick="sayHello()"  class="test">测试字体</button>
</body>
<script>
   async  function sayHello() {
        const res = await pywebview.api.say_hello('前端传值')
        alert(res)
    }
</script>
</html>