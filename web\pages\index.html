<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后台管理系统</title>

    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Element Plus Icons -->
    <link rel="stylesheet" href="https://unpkg.com/@element-plus/icons-vue/dist/index.css">
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <style>
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
        }

        .fade-enter-active, .fade-leave-active {
            transition: opacity 0.3s ease;
        }

        .fade-enter-from, .fade-leave-to {
            opacity: 0;
        }
    </style>
</head>
<body>
    <div id="app">
        <el-container style="height: 100vh;">
            <!-- 左侧导航栏 -->
            <el-aside width="250px" style="border-right: 1px solid var(--el-border-color);">
                <el-container direction="vertical" style="height: 100%;">
                    <!-- Logo 区域 -->
                    <el-header height="60px" style="display: flex; align-items: center; justify-content: center; border-bottom: 1px solid var(--el-border-color);">
                        <div style="text-align: center;">
                            <div style="font-size: 18px; font-weight: bold; color: var(--el-color-primary); margin-bottom: 4px;">🚀 管理系统</div>
                            <div style="font-size: 12px; color: var(--el-text-color-secondary);">Admin Dashboard</div>
                        </div>
                    </el-header>

                    <!-- 导航菜单 -->
                    <el-main style="padding: 0;">
                        <el-menu
                            :default-active="activeMenu"
                            @select="handleMenuSelect"
                            unique-opened
                            style="border-right: none;"
                        >
                            <el-menu-item index="home">
                                <el-icon><HomeFilled /></el-icon>
                                <span>首页</span>
                            </el-menu-item>

                            <el-sub-menu index="chanmama">
                                <template #title>
                                    <el-icon><TrendCharts /></el-icon>
                                    <span>蝉妈妈</span>
                                </template>
                                <el-menu-item index="chanmama-talent">
                                    <el-icon><UserFilled /></el-icon>
                                    <span>达人数据提取</span>
                                </el-menu-item>
                            </el-sub-menu>
                        </el-menu>
                    </el-main>
                </el-container>
            </el-aside>

            <!-- 右侧主内容区 -->
            <el-container direction="vertical">
                <!-- 顶部标题栏 -->
                <el-header height="60px" style="display: flex; align-items: center; justify-content: space-between; padding: 0 30px; border-bottom: 1px solid var(--el-border-color);">
                    <div>
                        <h2 style="margin: 0; color: var(--el-text-color-primary);">{{ currentPageTitle }}</h2>
                        <p style="margin: 4px 0 0 0; color: var(--el-text-color-secondary); font-size: 14px;">{{ currentPageSubtitle }}</p>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <el-button :icon="Refresh" circle @click="refreshContent"></el-button>
                        <el-button :icon="Setting" circle></el-button>
                        <el-button :icon="Bell" circle></el-button>
                    </div>
                </el-header>

                <!-- 主内容区 -->
                <el-main style="background: var(--el-bg-color-page); padding: 0; position: relative;">
                    <!-- Loading 遮罩 -->
                    <div v-if="pageLoading" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: rgba(255,255,255,0.8); display: flex; align-items: center; justify-content: center; z-index: 1000;">
                        <el-loading-spinner size="large"></el-loading-spinner>
                    </div>

                    <transition name="fade" mode="out-in">
                        <div v-if="activeMenu === 'home'" key="home">
                            <div class="iframe-container" style="height: calc(100vh - 120px); width: 100%;">
                                <iframe
                                    src="home.html"
                                    style="width: 100%; height: 100%; border: none;"
                                    @load="onIframeLoad"
                                ></iframe>
                            </div>
                        </div>

                        <div v-else-if="activeMenu === 'chanmama-talent'" key="talent">
                            <div class="iframe-container" style="height: calc(100vh - 120px); width: 100%;">
                                <iframe
                                    src="talent.html"
                                    style="width: 100%; height: 100%; border: none;"
                                    @load="onIframeLoad"
                                ></iframe>
                            </div>
                        </div>
                    </transition>
                </el-main>
            </el-container>
        </el-container>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>

    <script>
        const { createApp, ref, computed } = Vue;
        const { ElMessage } = ElementPlus;

        const app = createApp({
            setup() {
                const activeMenu = ref('home');
                const pageLoading = ref(false);

                const currentPageTitle = computed(() => {
                    const titles = {
                        'home': '首页概览',
                        'chanmama-talent': '达人数据提取'
                    };
                    return titles[activeMenu.value] || '首页概览';
                });

                const currentPageSubtitle = computed(() => {
                    const subtitles = {
                        'home': '系统运行状态一览',
                        'chanmama-talent': '蝉妈妈平台数据分析'
                    };
                    return subtitles[activeMenu.value] || '系统运行状态一览';
                });

                const handleMenuSelect = (index) => {
                    pageLoading.value = true;
                    activeMenu.value = index;
                    // 模拟加载时间
                    setTimeout(() => {
                        pageLoading.value = false;
                    }, 300);
                };

                const refreshContent = () => {
                    pageLoading.value = true;
                    setTimeout(() => {
                        pageLoading.value = false;
                        ElMessage.success('刷新完成');
                    }, 500);
                };

                const onIframeLoad = () => {
                    pageLoading.value = false;
                };

                return {
                    activeMenu,
                    pageLoading,
                    currentPageTitle,
                    currentPageSubtitle,
                    handleMenuSelect,
                    refreshContent,
                    onIframeLoad
                };
            }
        });

        // 注册所有图标
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }

        app.use(ElementPlus).mount('#app');
    </script>
</body>
</html>