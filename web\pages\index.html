<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后台管理系统</title>

    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Element Plus Icons -->
    <script src="//unpkg.com/@element-plus/icons-vue"></script>
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <style>
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .fade-enter-active, .fade-leave-active {
            transition: opacity 0.3s ease;
        }

        .fade-enter-from, .fade-leave-to {
            opacity: 0;
        }

        /* 统一的容器样式 */
        .el-container {
            height: 100vh;
            overflow: hidden;
        }

        /* 侧边栏统一样式 */
        .el-aside {
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
        }

        /* Logo区域统一样式 */
        .logo-header {
            background: #ffffff;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 菜单统一样式 */
        .el-menu {
            background: #ffffff !important;
            border: none !important;
        }

        .el-menu-item, .el-sub-menu__title {
            margin: 4px 12px;
            border-radius: 8px;
            transition: all 0.2s ease;
            color: #6b7280;
        }

        .el-menu-item:hover, .el-sub-menu__title:hover {
            background: #f3f4f6 !important;
            color: #374151 !important;
        }

        .el-menu-item.is-active {
            background: #f3f4f6 !important;
            color: #1f2937 !important;
            font-weight: 500;
        }

        .el-sub-menu.is-active .el-sub-menu__title {
            background: #f3f4f6 !important;
            color: #1f2937 !important;
            font-weight: 500;
        }

        /* 顶部标题栏统一样式 */
        .header-container {
            background: #ffffff;
            border-bottom: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        /* 按钮统一样式 */
        .header-btn {
            transition: all 0.2s ease;
            border: 1px solid #e5e7eb;
            background: #ffffff;
            color: #6b7280;
        }

        .header-btn:hover {
            background: #f9fafb;
            border-color: #d1d5db;
            color: #374151;
        }

        /* Loading样式 */
        .loading-overlay {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(2px);
        }

        /* iframe容器 */
        .iframe-container {
            background: #ffffff;
            overflow: hidden;
        }

        .iframe-container iframe {
            border: none;
        }

        /* 主内容区域 */
        .el-main {
            padding: 0;
            overflow: hidden;
            background: #f9fafb;
        }

        /* 页面标题样式 */
        .page-title {
            color: #1f2937;
            font-weight: 600;
            margin: 0;
        }

        .page-subtitle {
            color: #6b7280;
            font-size: 14px;
            margin: 4px 0 0 0;
        }
    </style>
</head>
<body>
    <div id="app">
        <el-container style="height: 100vh;">
            <!-- 左侧导航栏 -->
            <el-aside width="240px">
                <el-container direction="vertical" style="height: 100%;">
                    <!-- Logo 区域 -->
                    <el-header height="64px" class="logo-header">
                        <div style="text-align: center;">
                            <div style="font-size: 18px; font-weight: 600; margin-bottom: 2px; color: #1f2937;">
                                管理系统
                            </div>
                            <div style="font-size: 12px; color: #6b7280;">Admin Dashboard</div>
                        </div>
                    </el-header>

                    <!-- 导航菜单 -->
                    <el-main style="padding: 0;">
                        <el-menu
                            :default-active="activeMenu"
                            @select="handleMenuSelect"
                            unique-opened
                            style="border-right: none;"
                        >
                            <el-menu-item index="home">
                                <el-icon><House /></el-icon>
                                <span>首页</span>
                            </el-menu-item>

                            <el-sub-menu index="chanmama">
                                <template #title>
                                    <el-icon><TrendCharts /></el-icon>
                                    <span>蝉妈妈</span>
                                </template>
                                <el-menu-item index="chanmama-talent">
                                    <el-icon><User /></el-icon>
                                    <span>达人数据提取</span>
                                </el-menu-item>
                            </el-sub-menu>
                        </el-menu>
                    </el-main>
                </el-container>
            </el-aside>

            <!-- 右侧主内容区 -->
            <el-container direction="vertical">
                <!-- 顶部标题栏 -->
                <el-header height="64px" class="header-container" style="display: flex; align-items: center; justify-content: space-between; padding: 0 24px;">
                    <div>
                        <h2 class="page-title">{{ currentPageTitle }}</h2>
                        <p class="page-subtitle">{{ currentPageSubtitle }}</p>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <el-button class="header-btn" circle @click="refreshContent" :loading="pageLoading">
                            <el-icon><Refresh /></el-icon>
                        </el-button>
                        <el-button class="header-btn" circle>
                            <el-icon><Setting /></el-icon>
                        </el-button>
                        <el-button class="header-btn" circle>
                            <el-icon><Bell /></el-icon>
                        </el-button>
                        <el-dropdown>
                            <el-button class="header-btn" circle>
                                <el-icon><User /></el-icon>
                            </el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item>个人中心</el-dropdown-item>
                                    <el-dropdown-item>系统设置</el-dropdown-item>
                                    <el-dropdown-item divided>退出登录</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </el-header>

                <!-- 主内容区 -->
                <el-main class="el-main">
                    <!-- Loading 遮罩 -->
                    <div v-if="pageLoading" class="loading-overlay" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; display: flex; flex-direction: column; align-items: center; justify-content: center; z-index: 1000;">
                        <el-loading-spinner size="large"></el-loading-spinner>
                        <p style="margin-top: 16px; color: #6b7280; font-size: 14px;">页面加载中...</p>
                    </div>

                    <transition name="fade" mode="out-in">
                        <div v-if="activeMenu === 'home'" key="home">
                            <div class="iframe-container" style="height: calc(100vh - 64px); width: 100%;">
                                <iframe
                                    src="home.html"
                                    style="width: 100%; height: 100%;"
                                    @load="onIframeLoad"
                                ></iframe>
                            </div>
                        </div>

                        <div v-else-if="activeMenu === 'chanmama-talent'" key="talent">
                            <div class="iframe-container" style="height: calc(100vh - 64px); width: 100%;">
                                <iframe
                                    src="talent.html"
                                    style="width: 100%; height: 100%;"
                                    @load="onIframeLoad"
                                ></iframe>
                            </div>
                        </div>
                    </transition>
                </el-main>
            </el-container>
        </el-container>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>

    <script>
        const { createApp, ref, computed } = Vue;
        const { ElMessage } = ElementPlus;

        const app = createApp({
            setup() {
                const activeMenu = ref('home');
                const pageLoading = ref(false);

                const currentPageTitle = computed(() => {
                    const titles = {
                        'home': '首页概览',
                        'chanmama-talent': '达人数据提取'
                    };
                    return titles[activeMenu.value] || '首页概览';
                });

                const currentPageSubtitle = computed(() => {
                    const subtitles = {
                        'home': '系统运行状态一览',
                        'chanmama-talent': '蝉妈妈平台数据分析'
                    };
                    return subtitles[activeMenu.value] || '系统运行状态一览';
                });

                const handleMenuSelect = (index) => {
                    pageLoading.value = true;
                    activeMenu.value = index;
                    // 模拟加载时间
                    setTimeout(() => {
                        pageLoading.value = false;
                    }, 300);
                };

                const refreshContent = () => {
                    pageLoading.value = true;
                    setTimeout(() => {
                        pageLoading.value = false;
                        ElMessage.success('刷新完成');
                    }, 500);
                };

                const onIframeLoad = () => {
                    pageLoading.value = false;
                };

                return {
                    activeMenu,
                    pageLoading,
                    currentPageTitle,
                    currentPageSubtitle,
                    handleMenuSelect,
                    refreshContent,
                    onIframeLoad
                };
            }
        });

        // 注册所有图标
            Object.keys(ElementPlusIconsVue).forEach(key => {
            app.component(key, ElementPlusIconsVue[key]);
        });

        app.use(ElementPlus).mount('#app');
    </script>
</body>
</html>