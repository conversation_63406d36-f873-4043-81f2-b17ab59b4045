@echo off
chcp 65001 >nul
title 办公辅助系统 - 打包工具

echo 🏗️ 办公辅助系统打包工具
echo ================================================

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    pause
    exit /b 1
)

REM 检查PyInstaller
pip show pyinstaller >nul 2>&1
if errorlevel 1 (
    echo ⚠️ PyInstaller未安装，正在安装...
    pip install pyinstaller
    if errorlevel 1 (
        echo ❌ PyInstaller安装失败
        pause
        exit /b 1
    )
)

REM 检查文件
if not exist main.py (
    echo ❌ main.py 文件不存在
    pause
    exit /b 1
)

if not exist web (
    echo ❌ web 目录不存在
    pause
    exit /b 1
)

echo ✅ 环境检查通过

REM 清理旧文件
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist *.spec del *.spec

echo 🧹 清理完成

REM 开始打包
echo 📦 开始打包...
pyinstaller --onefile --windowed --name=办公辅助系统 --add-data=web;web --hidden-import=openpyxl --hidden-import=requests --hidden-import=webview --hidden-import=apis --hidden-import=cmm --hidden-import=sqlite3_util --clean main.py

if errorlevel 1 (
    echo ❌ 打包失败
    pause
    exit /b 1
)

REM 检查结果
if exist "dist\办公辅助系统.exe" (
    echo ✅ 打包成功！
    echo 📁 输出文件: dist\办公辅助系统.exe
    
    REM 显示文件大小
    for %%I in ("dist\办公辅助系统.exe") do echo 📊 文件大小: %%~zI bytes
    
    echo.
    echo 🎉 打包完成！可以在 dist 目录找到可执行文件
) else (
    echo ❌ 未找到输出文件
)

pause
