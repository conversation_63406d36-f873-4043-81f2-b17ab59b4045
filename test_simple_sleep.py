#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试休眠功能
"""

import time
from cmm import get_token_from_db, get_real_info

def test_simple_sleep():
    """简单测试休眠功能"""
    print("🧪 简单测试休眠功能...")
    
    # 获取token
    token = get_token_from_db()
    if not token:
        print("❌ 数据库中没有token，请先登录蝉妈妈")
        return False
    
    print(f"🔑 使用token: {token[:20]}...")
    
    # 测试2个ID
    test_ids = [
        'Te4oLu6PzddK8v0S_JURlE20CMuhagMW',
        'bV-1-fGqiHdZgr2cKWwomg'
    ]
    
    print(f"📋 测试 {len(test_ids)} 个ID，每个间隔3秒...")
    
    for i, test_id in enumerate(test_ids, 1):
        print(f"\n📡 [{i}/{len(test_ids)}] 测试ID: {test_id}")
        
        try:
            result = get_real_info(test_id, token)
            
            if result and result.get('signature') and result.get('unique_id'):
                print(f"✅ 获取成功")
                print(f"   📝 signature: {result['signature'][:50]}...")
                print(f"   🆔 unique_id: {result['unique_id']}")
            else:
                print(f"⚠️ 数据不完整: {str(result)[:100]}...")
                
        except Exception as e:
            print(f"❌ 获取异常: {str(e)}")
        
        # 添加休眠（除了最后一个）
        if i < len(test_ids):
            print(f"⏱️ 休眠3秒...")
            time.sleep(3)
    
    print("\n✅ 测试完成！")
    return True

if __name__ == "__main__":
    test_simple_sleep()
