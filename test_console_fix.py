#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试控制台输出修复
"""

def test_update_processing_progress():
    """测试update_processing_progress方法"""
    print("🧪 测试update_processing_progress方法...")
    
    try:
        from apis import API
        
        api = API()
        
        # 测试不传参数，只获取当前状态
        result1 = api.update_processing_progress()
        print(f"✅ 无参数调用成功")
        print(f"📊 返回数据类型: {type(result1)}")
        print(f"📊 包含字段: {list(result1.keys()) if isinstance(result1, dict) else 'Not dict'}")
        
        # 测试传入参数更新状态
        result2 = api.update_processing_progress(current=3, total=10, message="正在处理第3个项目")
        print(f"✅ 带参数调用成功")
        print(f"📊 当前状态: {result2.get('current_status', 'unknown')}")
        print(f"📊 当前消息: {result2.get('current_message', 'unknown')}")
        print(f"📊 进度: {result2.get('progress', 0)}%")
        print(f"📊 日志数量: {len(result2.get('logs', []))}")
        
        # 显示最新日志
        if result2.get('logs'):
            print("📋 最新日志:")
            for i, log in enumerate(result2['logs'][-3:], 1):
                print(f"   {i}. [{log['time_str']}] {log['type']}: {log['message']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_console_output_in_process_file():
    """测试process_file中的控制台输出"""
    print("\n🧪 测试process_file中的控制台输出...")
    
    try:
        from apis import API, get_console_output
        
        api = API()
        
        # 清空之前的日志
        from apis import GLOBAL_CONSOLE_OUTPUT
        GLOBAL_CONSOLE_OUTPUT["logs"] = []
        
        # 模拟文件数据
        mock_file_data = {
            "name": "test.xlsx",
            "content": [80, 75, 3, 4, 20, 0, 0, 0],  # 简化的Excel文件头
            "size": 1024,
            "crawlConfig": {
                "sleepInterval": 1
            }
        }
        
        print("📤 开始调用process_file...")
        
        # 调用process_file（这会在处理过程中添加控制台日志）
        try:
            result = api.process_file(mock_file_data)
            print(f"📊 process_file返回: {result.get('success', False)}")
        except Exception as e:
            print(f"⚠️ process_file异常（预期的）: {str(e)[:100]}...")
        
        # 检查控制台输出
        console_output = get_console_output()
        
        print(f"✅ 控制台输出检查完成")
        print(f"📊 日志数量: {len(console_output['logs'])}")
        print(f"📊 当前状态: {console_output['current_status']}")
        print(f"📊 是否处理中: {console_output['is_processing']}")
        
        # 显示所有日志
        if console_output['logs']:
            print("📋 所有控制台日志:")
            for i, log in enumerate(console_output['logs'], 1):
                print(f"   {i}. [{log['time_str']}] {log['type']}: {log['message']}")
        else:
            print("⚠️ 没有找到控制台日志")
        
        return len(console_output['logs']) > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_frontend_integration():
    """测试前端集成"""
    print("\n🧪 测试前端集成...")
    
    try:
        from apis import API
        
        api = API()
        
        # 模拟前端调用update_processing_progress
        print("📱 模拟前端轮询调用...")
        
        for i in range(5):
            result = api.update_processing_progress()
            print(f"📊 第{i+1}次调用:")
            print(f"   状态: {result.get('current_status', 'unknown')}")
            print(f"   消息: {result.get('current_message', 'unknown')}")
            print(f"   进度: {result.get('progress', 0)}%")
            print(f"   日志数: {len(result.get('logs', []))}")
            
            if result.get('logs'):
                latest_log = result['logs'][-1]
                print(f"   最新日志: [{latest_log['time_str']}] {latest_log['message']}")
        
        print("✅ 前端集成测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 前端集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def show_implementation_summary():
    """显示实现总结"""
    print("\n" + "="*60)
    print("📋 控制台输出修复总结")
    print("="*60)
    
    print("\n✅ 按需求实现:")
    print("1. 🔧 process_file方法中封装好数据后输出到控制台")
    print("   • 每个关键步骤都添加了add_console_log()")
    print("   • 将所有print()语句改为控制台日志")
    print("   • 实时更新全局变量GLOBAL_CONSOLE_OUTPUT")
    
    print("\n2. 🌐 修改全局变量存储日志")
    print("   • GLOBAL_CONSOLE_OUTPUT存储所有控制台日志")
    print("   • 包含状态、进度、消息等信息")
    print("   • 线程安全，使用锁保护")
    
    print("\n3. 📡 前端调用update_processing_progress返回全局变量")
    print("   • update_processing_progress()现在返回get_console_output()")
    print("   • 前端可以实时获取控制台输出")
    print("   • 支持带参数更新进度，不带参数只获取状态")
    
    print("\n4. 🔄 实时更新，不等process_file返回")
    print("   • process_file在处理过程中实时更新日志")
    print("   • 前端轮询update_processing_progress获取实时状态")
    print("   • 不需要等待整个异步任务结束")
    
    print("\n🎯 解决的问题:")
    print("❌ 原问题: 前端控制台没有输出")
    print("✅ 现在: process_file中每个步骤都有控制台日志")
    print("❌ 原问题: 找不到update_processing_progress调用")
    print("✅ 现在: 前端调用update_processing_progress获取实时日志")
    print("❌ 原问题: process_file返回代表任务结束")
    print("✅ 现在: 处理过程中实时更新，前端轮询获取")
    
    print("\n💡 使用方式:")
    print("1. 前端调用process_file开始处理")
    print("2. 前端定时调用update_processing_progress获取实时状态")
    print("3. 在工作状态卡片中显示控制台输出")
    print("4. 看到详细的处理步骤和进度")

def run_all_tests():
    """运行所有测试"""
    print("🧪 测试控制台输出修复")
    print("="*60)
    
    tests = [
        ("update_processing_progress方法测试", test_update_processing_progress),
        ("process_file控制台输出测试", test_console_output_in_process_file),
        ("前端集成测试", test_frontend_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"{'✅' if result else '❌'} {test_name}: {'通过' if result else '失败'}")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {str(e)}")
            results.append((test_name, False))
    
    # 显示实现总结
    show_implementation_summary()
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 测试总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {status} {test_name}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有测试通过！控制台输出功能已按需求完美实现！")
        print("\n💡 现在前端可以:")
        print("   1. 调用process_file开始处理")
        print("   2. 定时调用update_processing_progress获取实时日志")
        print("   3. 在工作状态卡片中显示控制台输出")
        print("   4. 看到详细的处理步骤和进度")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    run_all_tests()
