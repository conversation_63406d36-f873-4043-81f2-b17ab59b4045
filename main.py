import webview
import os
from apis import API
from sqlite3_util import init_database


def main():
    print("🚀 启动办公辅助系统...")

    # 初始化数据库
    print("\n📦 初始化数据库...")
    db_init_success = init_database()
    if not db_init_success:
        print("❌ 数据库初始化失败，程序退出")
        return

    print("\n🔧 初始化API...")
    api = API()
    # 获取web目录和HTML文件路径
    print("\n🌐 准备启动窗体界面...")
    current_dir = os.path.dirname(os.path.abspath(__file__))
    web_dir = os.path.join(current_dir, "web")
    html_path = os.path.join(web_dir, "pages", "index.html")

    print(f"   Web目录: {web_dir}")
    print(f"   HTML文件: {html_path}")

    # 确保文件存在
    if not os.path.exists(html_path):
        print(f"❌ GUI文件不存在: {html_path}")
        return
    else:
        print("✅ GUI文件存在，准备启动...")

    # 创建webview窗口
    print("\n🖥️  创建应用窗口...")
    webview.create_window(
        title="办公辅助系统",
        url=html_path,
        width=1400,
        height=900,
        min_size=(1000, 700),
        resizable=True,
        maximized=False,
        js_api=api
    )
    print("🚀 启动应用...")
    # 启动webview（开启调试模式）
    webview.start(debug=True)
    print("👋 应用已关闭")


if __name__ == "__main__":
    main()