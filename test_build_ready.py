#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试打包准备情况
"""

import sys
import os
import importlib

def test_python_version():
    """测试Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    
    if version.major >= 3 and version.minor >= 7:
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"❌ Python版本过低: {version.major}.{version.minor}.{version.micro}")
        print("需要Python 3.7+")
        return False

def test_required_packages():
    """测试必需包"""
    print("\n📦 检查必需包...")
    
    required_packages = {
        'webview': 'pywebview',
        'openpyxl': 'openpyxl', 
        'requests': 'requests',
        'sqlite3': 'sqlite3',
        'json': 'json',
        'time': 'time',
        'threading': 'threading'
    }
    
    results = {}
    
    for package, display_name in required_packages.items():
        try:
            importlib.import_module(package)
            print(f"✅ {display_name}")
            results[package] = True
        except ImportError:
            print(f"❌ {display_name} - 未安装")
            results[package] = False
    
    return all(results.values())

def test_optional_packages():
    """测试可选包"""
    print("\n📦 检查可选包...")
    
    optional_packages = {
        'PyInstaller': 'pyinstaller'
    }
    
    for package, display_name in optional_packages.items():
        try:
            importlib.import_module(package.lower())
            print(f"✅ {display_name}")
        except ImportError:
            print(f"⚠️ {display_name} - 未安装（打包时需要）")

def test_local_modules():
    """测试本地模块"""
    print("\n🔧 检查本地模块...")
    
    local_modules = ['apis', 'cmm', 'sqlite3_util']
    results = {}
    
    for module in local_modules:
        try:
            importlib.import_module(module)
            print(f"✅ {module}.py")
            results[module] = True
        except ImportError as e:
            print(f"❌ {module}.py - 导入失败: {e}")
            results[module] = False
    
    return all(results.values())

def test_file_structure():
    """测试文件结构"""
    print("\n📁 检查文件结构...")
    
    required_files = [
        'main.py',
        'apis.py', 
        'cmm.py',
        'sqlite3_util.py',
        'web/pages/talent.html'
    ]
    
    optional_files = [
        'requirements.txt',
        'build.py',
        'start.bat',
        'start.sh',
        'BUILD.md',
        'icon.ico'
    ]
    
    results = {}
    
    print("必需文件:")
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
            results[file] = True
        else:
            print(f"❌ {file} - 文件不存在")
            results[file] = False
    
    print("\n可选文件:")
    for file in optional_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"⚠️ {file} - 文件不存在")
    
    return all(results.values())

def test_web_resources():
    """测试Web资源"""
    print("\n🌐 检查Web资源...")
    
    web_files = [
        'web/pages/talent.html'
    ]
    
    results = {}
    
    for file in web_files:
        if os.path.exists(file):
            # 检查文件大小
            size = os.path.getsize(file)
            print(f"✅ {file} ({size} bytes)")
            results[file] = True
        else:
            print(f"❌ {file} - 文件不存在")
            results[file] = False
    
    return all(results.values())

def test_database():
    """测试数据库"""
    print("\n🗄️ 检查数据库...")
    
    try:
        from sqlite3_util import init_database
        result = init_database()
        
        if result:
            print("✅ 数据库初始化成功")
            return True
        else:
            print("⚠️ 数据库已存在")
            return True
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

def show_build_instructions():
    """显示打包说明"""
    print("\n" + "="*60)
    print("📋 打包说明")
    print("="*60)
    
    print("\n🚀 打包步骤:")
    print("1. 安装PyInstaller:")
    print("   pip install pyinstaller")
    
    print("\n2. 运行打包脚本:")
    print("   python build.py")
    
    print("\n3. 或手动打包:")
    print("   pyinstaller --onefile --windowed --name=办公辅助系统 --add-data=web;web main.py")
    
    print("\n📁 输出位置:")
    print("   dist/办公辅助系统.exe")
    
    print("\n⚠️ 注意事项:")
    print("• 确保所有依赖已安装")
    print("• 在目标系统上测试")
    print("• 检查文件大小和启动速度")

def main():
    """主函数"""
    print("🏗️ 办公辅助系统 - 打包准备检查")
    print("="*60)
    
    tests = [
        ("Python版本", test_python_version),
        ("必需包", test_required_packages),
        ("本地模块", test_local_modules),
        ("文件结构", test_file_structure),
        ("Web资源", test_web_resources),
        ("数据库", test_database)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 检查可选包
    test_optional_packages()
    
    # 显示总结
    print("\n" + "="*60)
    print("📊 检查总结")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有检查通过！可以开始打包了！")
        show_build_instructions()
    else:
        print("\n⚠️ 部分检查失败，请先解决问题")
        print("\n💡 解决建议:")
        print("• 安装缺失的依赖: pip install -r requirements.txt")
        print("• 检查文件路径和权限")
        print("• 确保Python版本符合要求")

if __name__ == "__main__":
    main()
