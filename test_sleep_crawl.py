#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试带休眠的爬取功能
"""

import time
from apis import API
from cmm import get_token_from_db, get_real_info

def test_sleep_crawl():
    """测试带休眠的爬取功能"""
    print("🧪 测试带休眠的爬取功能...")
    
    # 获取token
    token = get_token_from_db()
    if not token:
        print("❌ 数据库中没有token，请先登录蝉妈妈")
        return False
    
    print(f"🔑 使用token: {token[:20]}...")
    
    # 测试ID列表（更多ID测试休眠效果）
    test_ids = [
        'Te4oLu6PzddK8v0S_JURlE20CMuhagMW',
        'bV-1-fGqiHdZgr2cKWwomg',
        'Te4oLu6PzddK8v0S_JURlE20CMuhagMW',  # 重复测试
    ]
    
    print(f"📋 开始测试 {len(test_ids)} 个ID，每个间隔3秒...")
    start_time = time.time()
    
    results = []
    
    for i, test_id in enumerate(test_ids, 1):
        print(f"\n📡 [{i}/{len(test_ids)}] 测试ID: {test_id}")
        request_start = time.time()
        
        try:
            result = get_real_info(test_id, token)
            request_time = time.time() - request_start
            
            if result:
                print(f"✅ 获取成功 (耗时: {request_time:.2f}秒)")
                
                # 检查数据完整性
                has_signature = 'signature' in result and result['signature']
                has_unique_id = 'unique_id' in result and result['unique_id']
                
                result_info = {
                    'id': test_id,
                    'success': True,
                    'has_signature': has_signature,
                    'has_unique_id': has_unique_id,
                    'signature': result.get('signature', '')[:50] + '...' if result.get('signature') else '',
                    'unique_id': result.get('unique_id', ''),
                    'request_time': request_time
                }
                
                if has_signature and has_unique_id:
                    print(f"   📝 signature: {result['signature'][:30]}...")
                    print(f"   🆔 unique_id: {result['unique_id']}")
                    print(f"   ✅ 数据完整")
                else:
                    print(f"   ⚠️ 数据不完整 - signature: {has_signature}, unique_id: {has_unique_id}")
                    
            else:
                print(f"❌ 获取失败 (耗时: {request_time:.2f}秒)")
                result_info = {
                    'id': test_id,
                    'success': False,
                    'has_signature': False,
                    'has_unique_id': False,
                    'signature': '',
                    'unique_id': '',
                    'request_time': request_time
                }
            
            results.append(result_info)
            
            # 添加休眠（除了最后一个）
            if i < len(test_ids):
                print(f"⏱️ 休眠3秒，避免请求过快...")
                time.sleep(3)
                
        except Exception as e:
            print(f"❌ 获取异常: {str(e)}")
            results.append({
                'id': test_id,
                'success': False,
                'has_signature': False,
                'has_unique_id': False,
                'signature': '',
                'unique_id': '',
                'request_time': 0,
                'error': str(e)
            })
    
    total_time = time.time() - start_time
    
    # 统计结果
    print(f"\n📊 爬取结果统计:")
    print(f"   总耗时: {total_time:.2f}秒")
    print(f"   总请求: {len(test_ids)} 个")
    print(f"   平均耗时: {total_time/len(test_ids):.2f}秒/个")
    
    success_count = sum(1 for r in results if r['success'])
    complete_count = sum(1 for r in results if r['has_signature'] and r['has_unique_id'])
    
    print(f"   成功请求: {success_count}/{len(test_ids)} ({success_count/len(test_ids)*100:.1f}%)")
    print(f"   数据完整: {complete_count}/{len(test_ids)} ({complete_count/len(test_ids)*100:.1f}%)")
    
    # 详细结果
    print(f"\n📋 详细结果:")
    for i, result in enumerate(results, 1):
        status = "✅" if result['success'] else "❌"
        complete = "🟢" if result['has_signature'] and result['has_unique_id'] else "🟡"
        print(f"   {i}. {status} {complete} {result['id'][:20]}... ({result['request_time']:.2f}s)")
        if result['success']:
            print(f"      signature: {result['signature']}")
            print(f"      unique_id: {result['unique_id']}")
        elif 'error' in result:
            print(f"      error: {result['error'][:50]}...")
    
    return complete_count == len(test_ids)

def simulate_process_file_with_sleep():
    """模拟带休眠的process_file处理"""
    print("\n🧪 模拟带休眠的process_file处理...")
    
    # 模拟超链接数据
    mock_hyperlinks = [
        {
            'row': 2,
            'col': 1,
            'column_name': '用户名',
            'cell_value': '测试用户1',
            'hyperlink': 'https://www.chanmama.com/web/authorDetail/Te4oLu6PzddK8v0S_JURlE20CMuhagMW'
        },
        {
            'row': 3,
            'col': 1,
            'column_name': '用户名',
            'cell_value': '测试用户2',
            'hyperlink': 'https://www.chanmama.com/web/authorDetail/bV-1-fGqiHdZgr2cKWwomg'
        },
        {
            'row': 4,
            'col': 1,
            'column_name': '用户名',
            'cell_value': '测试用户3',
            'hyperlink': 'https://www.chanmama.com/web/authorDetail/Te4oLu6PzddK8v0S_JURlE20CMuhagMW'
        }
    ]
    
    print(f"📋 模拟处理 {len(mock_hyperlinks)} 个超链接，预计耗时: {(len(mock_hyperlinks)-1)*3 + len(mock_hyperlinks)*2:.0f}秒")
    
    # 获取token
    api = API()
    token = api.get_latest_token_from_db()
    if not token:
        print("❌ 未找到有效token")
        return False
    
    print(f"🔑 使用token: {token[:20]}...")
    
    # 模拟process_file的处理逻辑
    import re
    from datetime import datetime
    
    start_time = time.time()
    sqlite_data = []
    
    for i, link in enumerate(mock_hyperlinks, 1):
        # 提取ID
        url = link['hyperlink']
        match = re.search(r'authorDetail/([^/?]+)', url)
        if match:
            author_id = match.group(1)
            
            print(f"\n📡 [{i}/{len(mock_hyperlinks)}] 处理达人: {author_id}")
            
            # 默认数据
            default_intro = "专业达人，内容优质"
            default_unique_id = f"user_{author_id[:8]}"
            
            # 获取真实数据
            real_intro = default_intro
            real_unique_id = default_unique_id
            
            try:
                from cmm import get_real_info
                real_data = get_real_info(author_id, token)
                
                if real_data and real_data.get('signature') and real_data.get('unique_id'):
                    real_intro = real_data['signature']
                    real_unique_id = real_data['unique_id']
                    print(f"✅ 获取成功 - 简介: {real_intro[:30]}... | 抖音ID: {real_unique_id}")
                else:
                    print(f"⚠️ API返回数据不完整，使用默认数据")
                    
            except Exception as e:
                print(f"❌ API调用失败: {str(e)}, 使用默认数据")
            
            # 组装数据
            data_row = {
                'file_name': 'test_file.xlsx',
                'username': link['cell_value'],
                'intro': real_intro,
                'unique_id': real_unique_id,
                'cmm_id': author_id,
                'create_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            sqlite_data.append(data_row)
            print(f"📝 数据组装完成 - 用户: {link['cell_value']} | 抖音ID: {real_unique_id}")
            
            # 添加休眠（除了最后一个）
            if i < len(mock_hyperlinks):
                print(f"⏱️ 休眠3秒，避免请求过快...")
                time.sleep(3)
    
    total_time = time.time() - start_time
    
    # 显示结果
    print(f"\n📊 处理完成:")
    print(f"   总耗时: {total_time:.2f}秒")
    print(f"   处理数量: {len(sqlite_data)} 条")
    print(f"   平均耗时: {total_time/len(sqlite_data):.2f}秒/条")
    
    return True

if __name__ == "__main__":
    print("🧪 测试带休眠的爬取功能...")
    
    # 测试单独的API调用
    test_sleep_crawl()
    
    # 模拟process_file处理
    simulate_process_file_with_sleep()
    
    print("\n✅ 所有测试完成！")
