#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
蝉妈妈集成测试脚本
测试完整的调用链：前端 → apis.py → cmm.py → 数据库
"""

from sqlite3_util import init_database
from apis import API
from cmm import get_latest_token, get_all_tokens

def test_integration():
    """测试完整集成"""
    print("🧪 开始蝉妈妈集成测试...")
    
    # 1. 初始化数据库
    print("\n1️⃣ 初始化数据库...")
    db_success = init_database()
    if not db_success:
        print("❌ 数据库初始化失败")
        return False
    print("✅ 数据库初始化成功")
    
    # 2. 创建API实例
    print("\n2️⃣ 创建API实例...")
    api = API()
    print("✅ API实例创建成功")
    
    # 3. 测试蝉妈妈登录API
    print("\n3️⃣ 测试蝉妈妈登录API...")
    try:
        result = api.api_login_cmm()
        print(f"登录API返回: {result}")
        
        if result.get('success') and result.get('logged_in'):
            print("✅ 蝉妈妈登录API测试成功")
            print(f"   Token: {result.get('token', '')[:20]}...")
        else:
            print("❌ 蝉妈妈登录API测试失败")
            print(f"   错误信息: {result.get('message', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 蝉妈妈登录API异常: {str(e)}")
        return False
    
    # 4. 验证token保存
    print("\n4️⃣ 验证token保存...")
    latest_token = get_latest_token()
    if latest_token:
        print("✅ Token保存验证成功")
        print(f"   最新Token: {latest_token[:20]}...")
    else:
        print("❌ Token保存验证失败")
        return False
    
    # 5. 查看所有token
    print("\n5️⃣ 查看token列表...")
    tokens = get_all_tokens(limit=3)
    if tokens:
        print(f"✅ 找到 {len(tokens)} 个token")
    else:
        print("⚠️ 没有找到token")
    
    print("\n✅ 蝉妈妈集成测试完成！")
    return True

def test_api_only():
    """仅测试API层"""
    print("🧪 测试API层...")
    
    api = API()
    result = api.api_login_cmm()
    
    print(f"API返回结果:")
    print(f"  success: {result.get('success')}")
    print(f"  logged_in: {result.get('logged_in')}")
    print(f"  message: {result.get('message')}")
    print(f"  token: {result.get('token', '')[:30] if result.get('token') else 'None'}...")
    
    return result.get('success', False)

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == '--api-only':
        # 仅测试API
        test_api_only()
    else:
        # 完整集成测试
        test_integration()
