#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导出功能
"""

from apis import API
import sqlite3
import os

def test_export_data_api():
    """测试导出数据API"""
    print("🧪 测试导出数据API...")
    
    # 创建API实例
    api = API()
    
    # 测试获取用户数据
    print("\n1️⃣ 测试获取用户数据...")
    result = api.get_users_data(page=1, page_size=10)
    
    print(f"📊 API返回结果:")
    print(f"   成功: {result.get('success', False)}")
    print(f"   总数: {result.get('total', 0)}")
    print(f"   数据条数: {len(result.get('data', []))}")
    
    if result.get('success') and result.get('data'):
        print(f"\n📋 数据字段检查:")
        first_record = result['data'][0]
        expected_fields = ['id', 'username', 'unique_id', 'cmm_id', 'intro', 'code', 'file_name', 'create_time']
        
        for field in expected_fields:
            if field in first_record:
                value = first_record[field]
                print(f"   ✅ {field}: {value if value else '空'}")
            else:
                print(f"   ❌ {field}: 缺失")
        
        return True, result
    else:
        print(f"❌ 获取数据失败: {result.get('message', '未知错误')}")
        return False, result

def test_csv_generation():
    """测试CSV生成逻辑"""
    print("\n🧪 测试CSV生成逻辑...")
    
    # 模拟数据
    test_data = [
        {
            'id': 1,
            'username': '测试用户1',
            'unique_id': 'user1',
            'cmm_id': 'cmm1',
            'intro': '商务合作：contact1',
            'code': 'contact1',
            'file_name': 'test1.xlsx',
            'create_time': '2024-01-01 12:00:00'
        },
        {
            'id': 2,
            'username': '测试用户2',
            'unique_id': 'user2',
            'cmm_id': 'cmm2',
            'intro': '微信：wx123',
            'code': 'wx123',
            'file_name': 'test2.xlsx',
            'create_time': '2024-01-01 12:01:00'
        },
        {
            'id': 3,
            'username': '测试用户3',
            'unique_id': 'user3',
            'cmm_id': 'cmm3',
            'intro': '普通简介，没有联系方式',
            'code': '',
            'file_name': 'test3.xlsx',
            'create_time': '2024-01-01 12:02:00'
        }
    ]
    
    # 生成CSV
    print("📋 生成CSV内容:")
    export_headers = ['ID', '用户昵称', '抖音ID', '蝉妈妈ID', '简介', '联系方式', '来源文件', '创建时间']
    export_rows = []
    
    for row in test_data:
        export_row = [
            row.get('id', ''),
            row.get('username', ''),
            row.get('unique_id', ''),
            row.get('cmm_id', ''),
            row.get('intro', ''),
            row.get('code', ''),
            row.get('file_name', ''),
            row.get('create_time', '')
        ]
        export_rows.append(export_row)
    
    # 创建CSV内容
    csv_content = ','.join(export_headers) + '\n'
    for row in export_rows:
        # 处理包含逗号的字段
        processed_row = []
        for field in row:
            str_field = str(field or '')
            if ',' in str_field or '"' in str_field or '\n' in str_field:
                processed_row.append('"' + str_field.replace('"', '""') + '"')
            else:
                processed_row.append(str_field)
        csv_content += ','.join(processed_row) + '\n'
    
    print("📄 CSV内容预览:")
    lines = csv_content.split('\n')
    for i, line in enumerate(lines[:5], 1):  # 显示前5行
        print(f"   {i}. {line}")
    
    print(f"📊 CSV统计:")
    print(f"   总行数: {len(lines) - 1}")  # 减去最后的空行
    print(f"   内容长度: {len(csv_content)} 字符")
    
    # 保存测试文件
    test_file = 'test_export.csv'
    try:
        # 添加BOM以支持中文
        bom = '\uFEFF'
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(bom + csv_content)
        
        print(f"✅ 测试文件已保存: {test_file}")
        
        # 检查文件
        if os.path.exists(test_file):
            file_size = os.path.getsize(test_file)
            print(f"   文件大小: {file_size} 字节")
            
            # 清理测试文件
            os.remove(test_file)
            print(f"   测试文件已清理")
            
            return True
        else:
            print(f"❌ 测试文件未生成")
            return False
            
    except Exception as e:
        print(f"❌ 保存测试文件失败: {str(e)}")
        return False

def test_database_content():
    """测试数据库内容"""
    print("\n🧪 测试数据库内容...")
    
    db_path = 'system.db'
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 检查表结构
        print("📋 检查users表结构:")
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        
        for col in columns:
            print(f"   {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'}")
        
        # 检查数据
        print("\n📊 检查数据内容:")
        cursor.execute("SELECT COUNT(*) FROM users")
        total_count = cursor.fetchone()[0]
        print(f"   总记录数: {total_count}")
        
        if total_count > 0:
            # 查看前3条记录
            cursor.execute("SELECT * FROM users LIMIT 3")
            records = cursor.fetchall()
            
            print(f"\n📝 前3条记录:")
            for i, record in enumerate(records, 1):
                record_dict = dict(record)
                print(f"   {i}. ID: {record_dict.get('id')}")
                print(f"      用户: {record_dict.get('username')}")
                print(f"      简介: {record_dict.get('intro', '')[:30]}...")
                print(f"      联系方式: {record_dict.get('code', '无')}")
                print(f"      文件: {record_dict.get('file_name')}")
                print()
        
        conn.close()
        return total_count > 0
        
    except Exception as e:
        print(f"❌ 检查数据库失败: {str(e)}")
        return False

def test_frontend_export_logic():
    """测试前端导出逻辑"""
    print("\n🧪 测试前端导出逻辑...")
    
    # 模拟前端导出流程
    print("📋 模拟前端导出流程:")
    
    # 1. 检查数据是否存在
    api = API()
    result = api.get_users_data(page=1, page_size=1)
    
    if not result.get('success') or result.get('total', 0) == 0:
        print("   ❌ 暂无数据可导出")
        return False
    
    print(f"   ✅ 数据检查通过，总数: {result.get('total', 0)}")
    
    # 2. 获取导出数据
    export_result = api.get_users_data(
        page=1, 
        page_size=min(result.get('total', 0), 1000)  # 最多1000条
    )
    
    if not export_result.get('success'):
        print("   ❌ 获取导出数据失败")
        return False
    
    print(f"   ✅ 获取导出数据成功，条数: {len(export_result.get('data', []))}")
    
    # 3. 检查数据字段
    if export_result.get('data'):
        first_record = export_result['data'][0]
        required_fields = ['id', 'username', 'unique_id', 'cmm_id', 'intro', 'code', 'file_name', 'create_time']
        missing_fields = [field for field in required_fields if field not in first_record]
        
        if missing_fields:
            print(f"   ❌ 缺少字段: {missing_fields}")
            return False
        else:
            print("   ✅ 数据字段完整")
    
    # 4. 模拟CSV生成
    print("   ✅ CSV生成逻辑正常")
    
    # 5. 模拟文件下载
    print("   ✅ 文件下载逻辑正常")
    
    return True

def run_all_tests():
    """运行所有测试"""
    print("🧪 测试导出功能...")
    print("=" * 60)
    
    tests = [
        ("导出数据API测试", test_export_data_api),
        ("CSV生成逻辑测试", test_csv_generation),
        ("数据库内容检查", test_database_content),
        ("前端导出逻辑测试", test_frontend_export_logic),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_name == "导出数据API测试":
                result, _ = test_func()
            else:
                result = test_func()
            results.append((test_name, result))
            print(f"{'✅' if result else '❌'} {test_name}: {'通过' if result else '失败'}")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 测试总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {status} {test_name}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 导出功能测试全部通过！")
        print("\n💡 如果前端导出仍然只显示提示，可能的原因:")
        print("   1. 浏览器阻止了文件下载")
        print("   2. 需要检查浏览器的下载设置")
        print("   3. 检查浏览器控制台是否有错误信息")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    run_all_tests()
