import sqlite3
import os
from typing import List, Dict, Optional, Union, Tuple
# 创建数据库
def create_db(db_path='system.db'):
    try:
        if not os.path.exists(db_path):
            # 创建空数据库文件
            open(db_path, 'w').close()
            print(f"已创建空数据库: {os.path.abspath(db_path)}")
            return True
        else:
            print(f"数据库已存在: {os.path.abspath(db_path)}")
            return False
    except Exception as e:
        print(f"创建数据库失败: {str(e)}")
        return False

from typing import List, Optional

def create_table(
    db_path: str = 'system.db',
    table_name: str = None,
    columns: List[dict] = None,
    sql_statement: str = None
) -> bool:
    """
    在SQLite数据库中创建表
    
    参数:
        db_path: 数据库文件路径 (默认'system.db')
        table_name: 要创建的表名
        columns: 列定义列表 [{'name': 'id', 'type': 'INTEGER', 'constraints': 'PRIMARY KEY'}, ...]
        sql_statement: 直接提供完整的CREATE TABLE语句
        
    返回:
        bool: 是否创建成功
        
    注意:
        必须提供 columns 或 sql_statement 其中之一
    """
    if not (columns or sql_statement):
        print("错误：必须提供列定义或完整SQL语句")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        if sql_statement:
            # 使用直接提供的SQL语句
            cursor.execute(sql_statement)
        else:
            # 动态生成SQL语句
            columns_def = []
            for col in columns:
                col_def = f"{col['name']} {col['type']}"
                if 'constraints' in col:
                    col_def += f" {col['constraints']}"
                columns_def.append(col_def)
            
            create_sql = f"CREATE TABLE IF NOT EXISTS {table_name} ({', '.join(columns_def)})"
            cursor.execute(create_sql)
        
        conn.commit()
        return True
        
    except sqlite3.Error as e:
        print(f"创建表失败: {str(e)}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def table_exists(db_path: str, table_name: str) -> bool:
    """
    检查表是否已存在
    """
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
        return cursor.fetchone() is not None
    finally:
        conn.close()

def init_users_table(db_path: str = 'system.db') -> bool:
    """
    初始化 users 表
    包含字段：id, file_name, username, intro, unique_id, cmm_id, create_time
    """
    users_table_sql = """
    CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        file_name TEXT NOT NULL COMMENT '原始文件名',
        username TEXT NOT NULL COMMENT '用户昵称',
        intro TEXT COMMENT '简介信息',
        unique_id TEXT COMMENT '抖音ID',
        cmm_id TEXT COMMENT '蝉妈妈ID',
        create_time TEXT NOT NULL COMMENT '创建时间'
    )
    """

    try:
        if not table_exists(db_path, 'users'):
            result = create_table(db_path=db_path, sql_statement=users_table_sql)
            if result:
                print("✅ users 表创建成功")
                return True
            else:
                print("❌ users 表创建失败")
                return False
        else:
            print("ℹ️  users 表已存在，跳过创建")
            return True
    except Exception as e:
        print(f"❌ 初始化 users 表失败: {str(e)}")
        return False

def init_database(db_path: str = 'system.db') -> bool:
    """
    初始化数据库和所有表
    """
    print("🚀 开始初始化数据库...")

    # 1. 创建数据库文件
    create_db(db_path)

    # 2. 创建所有表
    tables_success = []

    # 创建 users 表
    users_result = init_users_table(db_path)
    tables_success.append(('users', users_result))

    # 统计结果
    success_count = sum(1 for _, success in tables_success if success)
    total_count = len(tables_success)

    print(f"\n📊 数据库初始化完成:")
    print(f"   数据库文件: {os.path.abspath(db_path)}")
    print(f"   表创建结果: {success_count}/{total_count} 成功")

    for table_name, success in tables_success:
        status = "✅" if success else "❌"
        print(f"   {status} {table_name}")

    return success_count == total_count

def query_table(
    db_path: str,
    table_name: str,
    columns: List[str] = ["*"],
    where: Optional[str] = None,
    params: Optional[Union[tuple, dict]] = None,
    order_by: Optional[str] = None,
    limit: Optional[int] = None
) -> List[Dict]:
    """
    通用SQLite表查询方法
    
    参数:
        db_path: 数据库文件路径
        table_name: 要查询的表名
        columns: 要查询的列名列表，默认查询所有列
        where: WHERE条件语句（不包含WHERE关键字）
        params: 查询参数（元组或字典）
        order_by: 排序条件（不包含ORDER BY关键字）
        limit: 返回结果数量限制
        
    返回:
        包含查询结果的字典列表，每个字典代表一行数据
    """
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row  # 使返回结果为字典形式
        cursor = conn.cursor()
        
        # 构建SQL语句
        query = f"SELECT {', '.join(columns)} FROM {table_name}"
        
        if where:
            query += f" WHERE {where}"
        
        if order_by:
            query += f" ORDER BY {order_by}"
            
        if limit:
            query += f" LIMIT {limit}"
        
        # 执行查询
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        # 获取结果并转换为字典列表
        results = [dict(row) for row in cursor.fetchall()]
        return results
        
    except sqlite3.Error as e:
        print(f"查询表 {table_name} 失败: {str(e)}")
        return []
    finally:
        if 'conn' in locals():
            conn.close()


def batch_insert(
    db_path: str,
    table_name: str,
    field_names: List[str],
    data: List[Tuple],
    batch_size: int = 100
) -> int:
    """
    指定表名的批量数据插入方法
    
    参数:
        db_path: 数据库文件路径
        table_name: 要插入数据的表名
        field_names: 字段名称列表
        data: 要插入的数据列表（每个元素是字段值的元组）
        batch_size: 每批插入的数据量（默认100）
        
    返回:
        成功插入的行数
    """
    inserted_rows = 0
    conn = None
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 构建INSERT语句
        placeholders = ', '.join(['?'] * len(field_names))
        sql = f"""
        INSERT INTO {table_name} 
        ({', '.join(field_names)})
        VALUES ({placeholders})
        """
        
        # 分批执行插入
        for i in range(0, len(data), batch_size):
            batch = data[i:i + batch_size]
            cursor.executemany(sql, batch)
            inserted_rows += len(batch)
            conn.commit()  # 每批提交一次
            
        return inserted_rows
        
    except sqlite3.Error as e:
        print(f"批量插入到表 {table_name} 失败: {str(e)}")
        return 0
    finally:
        if conn:
            conn.close()




if __name__ == "__main__":
    print('载入数据库')
    # 插入数据
    
   
