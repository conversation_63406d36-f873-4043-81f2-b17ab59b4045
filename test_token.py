#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Token功能测试脚本
测试蝉妈妈登录和token数据库操作
"""

import sys
import os
from sqlite3_util import init_database
from cmm import login_cmm, get_latest_token, get_all_tokens

def test_token_workflow():
    """测试完整的token工作流程"""
    print("🧪 开始测试Token工作流程...")
    
    # 1. 初始化数据库
    print("\n1️⃣ 初始化数据库...")
    db_success = init_database()
    if not db_success:
        print("❌ 数据库初始化失败")
        return False
    
    # 2. 测试登录并保存token
    print("\n2️⃣ 测试登录并保存token...")
    try:
        login_result = login_cmm()
        if login_result and login_result.get('data', {}).get('logged_in'):
            print("✅ 登录成功")
        else:
            print("❌ 登录失败")
            return False
    except Exception as e:
        print(f"❌ 登录异常: {str(e)}")
        return False
    
    # 3. 验证token是否保存到数据库
    print("\n3️⃣ 验证token保存...")
    latest_token = get_latest_token()
    if latest_token:
        print("✅ Token保存验证成功")
    else:
        print("❌ Token保存验证失败")
        return False
    
    # 4. 查看所有token
    print("\n4️⃣ 查看token列表...")
    tokens = get_all_tokens(limit=5)
    if tokens:
        print(f"✅ 找到 {len(tokens)} 个token")
    else:
        print("⚠️ 没有找到token")
    
    print("\n✅ Token工作流程测试完成！")
    return True

def interactive_test():
    """交互式测试"""
    while True:
        print("\n" + "="*50)
        print("🧪 Token功能测试")
        print("="*50)
        print("1. 初始化数据库")
        print("2. 执行登录")
        print("3. 获取最新token")
        print("4. 查看所有token")
        print("5. 完整工作流程测试")
        print("0. 退出")
        
        choice = input("\n请选择操作 (0-5): ").strip()
        
        if choice == '0':
            print("👋 退出测试")
            break
        elif choice == '1':
            print("\n🔧 初始化数据库...")
            result = init_database()
            print("✅ 初始化成功" if result else "❌ 初始化失败")
        elif choice == '2':
            print("\n🔑 执行登录...")
            try:
                result = login_cmm()
                if result and result.get('data', {}).get('logged_in'):
                    print("✅ 登录成功")
                else:
                    print("❌ 登录失败")
            except Exception as e:
                print(f"❌ 登录异常: {str(e)}")
        elif choice == '3':
            print("\n🔍 获取最新token...")
            token = get_latest_token()
            if token:
                print(f"✅ 最新token: {token[:30]}...")
            else:
                print("❌ 没有找到token")
        elif choice == '4':
            print("\n📋 查看所有token...")
            tokens = get_all_tokens(limit=10)
            if not tokens:
                print("❌ 没有找到任何token")
        elif choice == '5':
            test_token_workflow()
        else:
            print("❌ 无效选择")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == '--auto':
        # 自动测试模式
        test_token_workflow()
    else:
        # 交互式测试模式
        interactive_test()
