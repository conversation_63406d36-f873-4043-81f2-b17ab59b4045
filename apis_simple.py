#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的API处理文件
"""

import openpyxl
import time
import threading
from datetime import datetime
import re

# 全局进度变量
GLOBAL_PROGRESS = {
    "status": "idle",  # idle, processing, completed, error
    "total": 0,
    "current": 0,
    "percentage": 0.0,
    "message": "",
    "current_item": "",
    "current_file": "",
    "start_time": None,
    "estimated_remaining": "未知",
    "processed_items": [],
    "last_update": time.time(),
    "console_logs": []
}

def reset_global_progress():
    """重置全局进度"""
    global GLOBAL_PROGRESS
    GLOBAL_PROGRESS.update({
        "status": "idle",
        "total": 0,
        "current": 0,
        "percentage": 0.0,
        "message": "",
        "current_item": "",
        "current_file": "",
        "start_time": None,
        "estimated_remaining": "未知",
        "processed_items": [],
        "last_update": time.time(),
        "console_logs": []
    })

def add_console_log(log_message, log_type="info"):
    """添加控制台日志"""
    global GLOBAL_PROGRESS
    
    log_entry = {
        "timestamp": time.time(),
        "message": log_message,
        "type": log_type,
        "time_str": time.strftime("%H:%M:%S", time.localtime())
    }
    
    GLOBAL_PROGRESS["console_logs"].append(log_entry)
    
    # 限制日志数量
    if len(GLOBAL_PROGRESS["console_logs"]) > 50:
        GLOBAL_PROGRESS["console_logs"] = GLOBAL_PROGRESS["console_logs"][-50:]

def update_global_progress(**kwargs):
    """更新全局进度"""
    global GLOBAL_PROGRESS
    
    # 更新字段
    for key, value in kwargs.items():
        if key in GLOBAL_PROGRESS:
            GLOBAL_PROGRESS[key] = value
    
    # 计算百分比
    if GLOBAL_PROGRESS["total"] > 0:
        GLOBAL_PROGRESS["percentage"] = (GLOBAL_PROGRESS["current"] / GLOBAL_PROGRESS["total"]) * 100.0
    
    # 添加日志
    if "log_message" in kwargs:
        add_console_log(kwargs["log_message"])
    
    GLOBAL_PROGRESS["last_update"] = time.time()
    
    print(f"📊 进度更新: {GLOBAL_PROGRESS['current']}/{GLOBAL_PROGRESS['total']} ({GLOBAL_PROGRESS['percentage']:.1f}%) - {GLOBAL_PROGRESS['message']}")

class SimpleAPI:
    def __init__(self):
        print("🚀 SimpleAPI 初始化完成")
    
    def process_file(self, file_data):
        """
        处理文件 - 立即返回，后台处理
        """
        print("📤 收到文件处理请求")
        
        # 重置进度
        reset_global_progress()
        update_global_progress(
            status="processing",
            message="开始处理文件...",
            log_message="🚀 开始处理Excel文件..."
        )
        
        # 启动后台线程
        def background_process():
            try:
                self._do_process_file(file_data)
            except Exception as e:
                print(f"❌ 后台处理异常: {str(e)}")
                import traceback
                traceback.print_exc()
                update_global_progress(
                    status="error",
                    message=f"处理失败: {str(e)}",
                    log_message=f"❌ 处理异常: {str(e)}"
                )
        
        thread = threading.Thread(target=background_process)
        thread.daemon = True
        thread.start()
        
        # 立即返回
        return {
            "success": True,
            "message": "文件处理已开始，请查看进度",
            "processing": True
        }
    
    def _do_process_file(self, file_data):
        """实际的文件处理逻辑"""
        print("🔄 后台开始处理文件...")
        
        # 验证数据
        if not file_data or 'name' not in file_data or 'content' not in file_data:
            update_global_progress(
                status="error",
                message="文件数据无效",
                log_message="❌ 文件数据无效"
            )
            return
        
        print(f"📄 处理文件: {file_data['name']}")
        update_global_progress(
            current_file=file_data['name'],
            message="正在解析Excel文件...",
            log_message=f"📄 开始处理: {file_data['name']}"
        )
        
        try:
            # 转换文件内容
            file_content = bytes(file_data['content'])
            from io import BytesIO
            file_stream = BytesIO(file_content)
            
            # 解析Excel
            workbook = openpyxl.load_workbook(file_stream)
            sheet = workbook.active
            
            print(f"✅ Excel解析成功: {sheet.max_row}行 x {sheet.max_column}列")
            update_global_progress(
                message="Excel解析成功，开始扫描数据...",
                log_message=f"✅ Excel解析成功: {sheet.max_row}行 x {sheet.max_column}列"
            )
            
            # 读取表头
            headers = []
            for col in range(1, sheet.max_column + 1):
                cell_value = sheet.cell(row=1, column=col).value
                headers.append(str(cell_value) if cell_value else f"列{col}")
            
            # 扫描超链接
            hyperlinks_found = []
            total_rows = sheet.max_row - 1
            
            update_global_progress(
                total=total_rows,
                current=0,
                message=f"扫描 {total_rows} 行数据中的超链接...",
                log_message=f"🔍 开始扫描 {total_rows} 行数据"
            )
            
            for row in range(2, sheet.max_row + 1):
                # 更新进度
                if (row - 2) % 10 == 0:  # 每10行更新一次
                    update_global_progress(
                        current=row - 1,
                        current_item=f"扫描第 {row} 行",
                        message=f"扫描第 {row} 行..."
                    )
                
                for col in range(1, sheet.max_column + 1):
                    cell = sheet.cell(row=row, column=col)
                    if cell.hyperlink:
                        hyperlink_info = {
                            'row': row,
                            'col': col,
                            'column_name': headers[col-1] if col-1 < len(headers) else f"列{col}",
                            'cell_value': str(cell.value) if cell.value else "",
                            'hyperlink': cell.hyperlink.target if cell.hyperlink.target else str(cell.hyperlink)
                        }
                        hyperlinks_found.append(hyperlink_info)
                        
                        if len(hyperlinks_found) <= 3:  # 只打印前3个
                            print(f"🔗 发现超链接: {hyperlink_info['cell_value']} -> {hyperlink_info['hyperlink']}")
            
            print(f"✅ 扫描完成，发现 {len(hyperlinks_found)} 个超链接")
            update_global_progress(
                current=total_rows,
                message=f"发现 {len(hyperlinks_found)} 个超链接",
                log_message=f"✅ 扫描完成：{len(hyperlinks_found)} 个超链接"
            )
            
            # 如果有超链接，开始处理
            if hyperlinks_found:
                self._process_hyperlinks(hyperlinks_found, file_data)
            else:
                update_global_progress(
                    status="completed",
                    message="处理完成，但未发现超链接",
                    log_message="⚠️ 未发现超链接，处理完成"
                )
                
        except Exception as e:
            error_msg = f"文件处理失败: {str(e)}"
            print(f"❌ {error_msg}")
            import traceback
            traceback.print_exc()
            update_global_progress(
                status="error",
                message=error_msg,
                log_message=f"❌ 处理失败: {str(e)}"
            )
    
    def _process_hyperlinks(self, hyperlinks_found, file_data):
        """处理超链接"""
        print(f"🔗 开始处理 {len(hyperlinks_found)} 个超链接")
        
        # 获取配置
        crawl_config = file_data.get('crawlConfig', {})
        sleep_interval = crawl_config.get('sleepInterval', 3)
        
        update_global_progress(
            total=len(hyperlinks_found),
            current=0,
            message=f"开始获取 {len(hyperlinks_found)} 个达人信息...",
            log_message=f"🔗 发现 {len(hyperlinks_found)} 个超链接，开始获取达人信息"
        )
        
        # 模拟处理每个超链接
        for i, link in enumerate(hyperlinks_found, 1):
            update_global_progress(
                current=i,
                current_item=f"获取达人: {link['cell_value']}",
                message=f"正在获取第 {i}/{len(hyperlinks_found)} 个达人信息",
                log_message=f"📡 [{i}/{len(hyperlinks_found)}] 获取达人: {link['cell_value']}"
            )
            
            # 模拟处理时间
            time.sleep(0.5)
            
            # 休眠（除了最后一个）
            if i < len(hyperlinks_found):
                update_global_progress(
                    current_item="休眠中...",
                    log_message=f"⏱️ 休眠 {sleep_interval} 秒，避免请求过快"
                )
                time.sleep(sleep_interval)
            
            # 完成当前项目
            update_global_progress(
                current=i,
                current_item=f"已完成第{i}个达人",
                log_message=f"✅ 完成第 {i} 个达人信息获取"
            )
        
        # 处理完成
        update_global_progress(
            status="completed",
            message=f"处理完成！成功获取 {len(hyperlinks_found)} 个达人信息",
            log_message=f"🎉 Excel文件处理完成！成功获取 {len(hyperlinks_found)} 个达人信息"
        )
    
    def get_processing_status(self):
        """获取处理状态"""
        global GLOBAL_PROGRESS
        status = GLOBAL_PROGRESS.copy()
        status["timestamp"] = time.time()
        status["is_processing"] = status["status"] == "processing"
        status["is_completed"] = status["status"] == "completed"
        status["is_error"] = status["status"] == "error"
        return status

if __name__ == "__main__":
    # 测试
    api = SimpleAPI()
    
    # 模拟文件数据
    test_file = {
        "name": "test.xlsx",
        "content": [80, 75, 3, 4],  # 简化的Excel文件头
        "crawlConfig": {"sleepInterval": 1}
    }
    
    print("🧪 开始测试...")
    result = api.process_file(test_file)
    print(f"📊 返回结果: {result}")
    
    # 等待处理完成
    while True:
        status = api.get_processing_status()
        print(f"📈 当前状态: {status['status']} - {status['message']}")
        
        if status['status'] in ['completed', 'error']:
            break
        
        time.sleep(1)
    
    print("🎉 测试完成！")
