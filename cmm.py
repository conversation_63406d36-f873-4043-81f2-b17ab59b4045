import requests
import hashlib
import time
import sqlite3
from datetime import datetime
import json

base_url = "https://api-service.chanmama.com/v1/author/detail/info?author_id="
cookies='LOGIN-TOKEN-FORSNS=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcHBJZCI6MTAwMDAsImFwcFZlcnNpb24iOiIiLCJleHBpcmVfdGltZSI6MTc1NDUwNjgwMCwiaWF0IjoxNzUzOTY3NzM4LCJpZCI6MTQ2ODc2OTIsImtpZCI6IlVTRVItRVRMRjhTVEVKTkswLTRYWlhCOSIsInJrIjoiR0lsNmoifQ.PgN2VAsLyx-TrYjXx5qLPFMDNfTo0wVFxpiou5nshpM'
headers = {
            'origin': 'https://www.chanmama.com',
            'referer': 'https://www.chanmama.com/',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36',
            'x-client-hash': '4e75f486521cd9471142b5dd4ad628f72f616c41',
            'x-client-id': '407213695',
            'x-client-version': '1',
            'x-encrypt-version': '2',
            'x-platform-id': '10000',
            'cookie': cookies
        }
def get_real_info(id):
    url = base_url + id
    response = requests.get(url, headers=headers)
    #转换成json对象
    json_data = json.loads(json.dumps(response.json(),ensure_ascii=False,indent=2))
    return json_data

def get_latest_token(db_path='system.db'):
    """
    从数据库获取最新的token
    """
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 查询最新的token
        cursor.execute("""
            SELECT token, create_time
            FROM tokens
            ORDER BY create_time DESC
            LIMIT 1
        """)
        result = cursor.fetchone()
        conn.close()

        if result:
            token, create_time = result
            print(f"🔑 获取最新token: {token[:20]}... (创建时间: {create_time})")
            return token
        else:
            print("❌ 数据库中没有找到token")
            return None

    except sqlite3.Error as e:
        print(f"❌ 从数据库获取token失败: {str(e)}")
        return None

def get_all_tokens(db_path='system.db', limit=10):
    """
    获取所有token（用于调试）
    """
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute("""
            SELECT id, token, create_time
            FROM tokens
            ORDER BY create_time DESC
            LIMIT ?
        """, (limit,))

        results = [dict(row) for row in cursor.fetchall()]
        conn.close()

        print(f"📋 数据库中的token列表 (最新{len(results)}个):")
        for i, token_info in enumerate(results, 1):
            print(f"   {i}. ID:{token_info['id']} Token:{token_info['token'][:20]}... Time:{token_info['create_time']}")

        return results

    except sqlite3.Error as e:
        print(f"❌ 获取token列表失败: {str(e)}")
        return []

# 登录禅妈妈接口
def login_cmm():
    headers = {}

# 将密码通过 md5 加密
    password = "weekseven"
    md5_hash = hashlib.md5()
    md5_hash.update(password.encode())
    hex_digest = md5_hash.hexdigest()

    json_data = {
        'from_platform': None,
        'appId': 10000,
        'timeStamp': int(time.time()),
        'username': '18775642907',
        'password': hex_digest
    }
    response = requests.post('https://api-service.chanmama.com/v1/access/token', headers=headers, json=json_data)

    # 检查响应是否成功
    if response.status_code == 200:
        response_data = response.json()

        # 获取token和登录状态
        token = response_data['data']['token']
        logged_in = response_data['data']['logged_in']

        print(f"🔑 获取到token: {token[:20]}...")
        print(f"📊 登录状态: {logged_in}")

        # 写入token到tokens表
        if logged_in and token:
            try:
                # 连接数据库
                conn = sqlite3.connect('system.db')
                cursor = conn.cursor()

                # 检查tokens表是否存在
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='tokens'")
                table_exists = cursor.fetchone() is not None

                if table_exists:
                    # 插入新token
                    create_time = datetime.now().isoformat()
                    cursor.execute(
                        "INSERT INTO tokens (token, create_time) VALUES (?, ?)",
                        (token, create_time)
                    )
                    conn.commit()
                    token_id = cursor.lastrowid

                    print(f"✅ Token已保存到数据库，ID: {token_id}")

                    # 可选：清理旧token（保留最新的10个）
                    cursor.execute("""
                        DELETE FROM tokens
                        WHERE id NOT IN (
                            SELECT id FROM tokens
                            ORDER BY create_time DESC
                            LIMIT 10
                        )
                    """)
                    deleted_count = cursor.rowcount
                    if deleted_count > 0:
                        print(f"🧹 清理了 {deleted_count} 个旧token")

                    conn.commit()
                else:
                    print("❌ tokens表不存在，请先初始化数据库")

                conn.close()

            except sqlite3.Error as e:
                print(f"❌ 保存token到数据库失败: {str(e)}")
            except Exception as e:
                print(f"❌ 处理token时发生错误: {str(e)}")
        else:
            print("❌ 登录失败或token为空，未保存到数据库")

        return response_data
    else:
        print(f"❌ 请求失败，状态码: {response.status_code}")
        return {"success": False, "message": f"HTTP {response.status_code}"}

if __name__ == "__main__":
    print("🚀 测试蝉妈妈登录...")

    # 测试登录
    result = login_cmm()
    print(f"登录结果: {result}")

    # 获取最新token
    latest_token = get_latest_token()
    print(f"最新token: {latest_token[:20] if latest_token else 'None'}...")

