import requests
import hashlib
import time
import sqlite3
from datetime import datetime
import json
# Te4oLu6PzddK8v0S_JURlE20CMuhagMW
# {'errMsg': '您的账号在另一个设备登录，若非本人操作，请及时更改密码', 'rid': 'eb544f7685b6c2b830871ef834cb78ad', 'errCode': 40006}
# LOGIN-TOKEN-FORSNS=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcHBJZCI6MTAwMDAsImFwcFZlcnNpb24iOiIiLCJleHBpcmVfdGltZSI6MTc1NDUwNjgwMCwiaWF0IjoxNzUzOTY3NzM4LCJpZCI6MTQ2ODc2OTIsImtpZCI6IlVTRVItRVRMRjhTVEVKTkswLTRYWlhCOSIsInJrIjoiR0lsNmoifQ.PgN2VAsLyx-TrYjXx5qLPFMDNfTo0wVFxpiou5nshpM'
base_url = "https://api-service.chanmama.com/v1/author/detail/info?author_id="

def get_real_info(id, token):
    """
    获取达人详细信息
    :param id: 达人ID
    :param token: 蝉妈妈token
    :return: 达人信息json数据
    """
    # 动态构建headers，使用传入的token
    headers = {
        'origin': 'https://www.chanmama.com',
        'referer': 'https://www.chanmama.com/',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36',
        'x-client-hash': '4e75f486521cd9471142b5dd4ad628f72f616c41',
        'x-client-id': '407213695',
        'x-client-version': '1',
        'x-encrypt-version': '2',
        'x-platform-id': '10000',
        'cookie': f'LOGIN-TOKEN-FORSNS={token}' if token else ''
    }

    url = base_url + id
    response = requests.get(url, headers=headers)

    # 转换成json对象
    json_data = json.loads(json.dumps(response.json(), ensure_ascii=False, indent=2))
    # 返回json当中的signature和unique_id
    signature = json_data.get('data', {}).get('signature')
    unique_id = json_data.get('data', {}).get('unique_id')

    return {
        'signature': signature,
        'unique_id': unique_id
    }

def get_token_from_db(db_path='system.db'):
    """从数据库获取最新的token"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        cursor.execute("""
            SELECT token FROM tokens
            ORDER BY create_time DESC
            LIMIT 1
        """)
        result = cursor.fetchone()
        conn.close()

        if result:
            return result[0]
        else:
            return None
    except Exception as e:
        print(f"❌ 从数据库获取token失败: {str(e)}")
        return None

def get_latest_token(db_path='system.db'):
    """
    从数据库获取最新的token
    """
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 查询最新的token
        cursor.execute("""
            SELECT token, create_time
            FROM tokens
            ORDER BY create_time DESC
            LIMIT 1
        """)
        result = cursor.fetchone()
        conn.close()

        if result:
            token, create_time = result
            print(f"🔑 获取最新token: {token[:20]}... (创建时间: {create_time})")
            return token
        else:
            print("❌ 数据库中没有找到token")
            return None

    except sqlite3.Error as e:
        print(f"❌ 从数据库获取token失败: {str(e)}")
        return None


# 登录禅妈妈接口
def login_cmm(username, password):
    """
    蝉妈妈登录
    """
    headers = {}

    # 将密码通过 md5 加密
    md5_hash = hashlib.md5()
    md5_hash.update(password.encode())
    hex_digest = md5_hash.hexdigest()


    json_data = {
        'from_platform': None,
        'appId': 10000,
        'timeStamp': int(time.time()),
        'username': username,
        'password': hex_digest
    }
    response = requests.post('https://api-service.chanmama.com/v1/access/token', headers=headers, json=json_data)

    # 检查响应是否成功
    if response.status_code == 200:
        response_data = response.json()

        # 获取token和登录状态
        token = response_data['data']['token']
        logged_in = response_data['data']['logged_in']

        print(f"🔑 获取到token: {token[:20]}...")
        print(f"📊 登录状态: {logged_in}")

        # 写入token到tokens表
        if logged_in and token:
            try:
                # 连接数据库
                conn = sqlite3.connect('system.db')
                cursor = conn.cursor()

                # 检查tokens表是否存在
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='tokens'")
                table_exists = cursor.fetchone() is not None

                if table_exists:
                    # 插入新token
                    create_time = datetime.now().isoformat()
                    cursor.execute(
                        "INSERT INTO tokens (token, create_time) VALUES (?, ?)",
                        (token, create_time)
                    )
                    conn.commit()
                    token_id = cursor.lastrowid

                    print(f"✅ Token已保存到数据库，ID: {token_id}")

                    conn.commit()
                else:
                    print("❌ tokens表不存在，请先初始化数据库")

                conn.close()

            except sqlite3.Error as e:
                print(f"❌ 保存token到数据库失败: {str(e)}")
            except Exception as e:
                print(f"❌ 处理token时发生错误: {str(e)}")
        else:
            print("❌ 登录失败或token为空，未保存到数据库")

        return response_data
    else:
        print(f"❌ 请求失败，状态码: {response.status_code}")
        return {"success": False, "message": f"HTTP {response.status_code}"}
if __name__ == "__main__":
    print("🧪 测试蝉妈妈功能...")

    # 1. 测试从数据库读取token
    print("\n1️⃣ 测试从数据库读取token...")
    db_token = get_token_from_db()
    if db_token:
        print(f"✅ 从数据库读取到token: {db_token[:20]}...")

        # 2. 测试API调用
        print("\n2️⃣ 测试API调用...")
        try:
            test_id = 'Te4oLu6PzddK8v0S_JURlE20CMuhagMW'
            result = get_real_info(test_id, db_token)
            if result:
                print(f"✅ API调用成功: {str(result)}...")
            else:
                print("❌ API调用失败")
        except Exception as e:
            print(f"❌ API调用异常: {str(e)}")
    else:
        print("❌ 数据库中没有token，请先登录蝉妈妈")

    print("\n✅ 测试完成！")

