import requests
import hashlib
import time
import sqlite3
from datetime import datetime
import json
# Te4oLu6PzddK8v0S_JURlE20CMuhagMW
# {'errMsg': '您的账号在另一个设备登录，若非本人操作，请及时更改密码', 'rid': 'eb544f7685b6c2b830871ef834cb78ad', 'errCode': 40006}
# LOGIN-TOKEN-FORSNS=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcHBJZCI6MTAwMDAsImFwcFZlcnNpb24iOiIiLCJleHBpcmVfdGltZSI6MTc1NDUwNjgwMCwiaWF0IjoxNzUzOTY3NzM4LCJpZCI6MTQ2ODc2OTIsImtpZCI6IlVTRVItRVRMRjhTVEVKTkswLTRYWlhCOSIsInJrIjoiR0lsNmoifQ.PgN2VAsLyx-TrYjXx5qLPFMDNfTo0wVFxpiou5nshpM'
base_url = "https://api-service.chanmama.com/v1/author/detail/info?author_id="

def get_real_info(id, token):
    """
    获取达人详细信息
    :param id: 达人ID
    :param token: 蝉妈妈token
    :return: 达人信息json数据
    """
    # 动态构建headers，使用传入的token
    headers = {
        'origin': 'https://www.chanmama.com',
        'referer': 'https://www.chanmama.com/',
        'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Mobile/15E148 Safari/604.1',
        'x-client-hash': '4e75f486521cd9471142b5dd4ad628f72f616c4',
        'x-client-id': '342123125455',
        'x-client-version': '1',
        'x-encrypt-version': '2',
        'x-platform-id': '100000',
        'cookie': f'LOGIN-TOKEN-FORSNS={token}' if token else ''
    }

    url = base_url + id
    response = requests.get(url, headers=headers)

    # 转换成json对象
    json_data = json.loads(json.dumps(response.json(), ensure_ascii=False, indent=2))
    print(json_data)
    # 返回json当中的signature和unique_id
    signature = json_data.get('data', {}).get('signature')
    unique_id = json_data.get('data', {}).get('unique_id')

    return {
        'signature': signature,
        'unique_id': unique_id
    }

def extract_contact_code(signature):
    """
    从signature中提取联系方式
    支持多种格式：
    - 商务🌷合作：Yanyan1010666
    - 商务合作V：abc123456
    - 微信：test123
    - VX：contact888
    - 联系方式：user999
    等等
    """
    if not signature:
        return ''

    import re

    # 定义多种匹配模式（按优先级排序）
    patterns = [
        # 商务合作类（优化：支持更多变体）
        r'商务.*?[：:]\s*([A-Za-z0-9_.-]+)',
        r'合作.*?[：:]\s*([A-Za-z0-9_.-]+)',
        r'商V.*?[：:]\s*([A-Za-z0-9_.-]+)',
        r'商务V.*?[：:]\s*([A-Za-z0-9_.-]+)',

        # 微信类（优化：支持更多格式）
        r'微信[：:]\s*([A-Za-z0-9_.-]+)',
        r'VX[：:]\s*([A-Za-z0-9_.-]+)',
        r'vx[：:]\s*([A-Za-z0-9_.-]+)',
        r'V[：:]\s*([A-Za-z0-9_.-]+)',
        r'wx[：:]\s*([A-Za-z0-9_.-]+)',
        r'WX[：:]\s*([A-Za-z0-9_.-]+)',

        # 联系方式类（优化：支持更多表达）
        r'联系.*?[：:]\s*([A-Za-z0-9_.-]+)',
        r'咨询.*?[：:]\s*([A-Za-z0-9_.-]+)',
        r'加.*?[：:]\s*([A-Za-z0-9_.-]+)',
        r'找.*?[：:]\s*([A-Za-z0-9_.-]+)',

        # 邮箱格式（优化：支持完整邮箱）
        r'([A-Za-z0-9_.-]+@[A-Za-z0-9_.-]+\.[A-Za-z]{2,})',
        r'邮箱.*?[：:]\s*([A-Za-z0-9_.-]+@[A-Za-z0-9_.-]+\.[A-Za-z]{2,})',

        # 手机号格式（优化：支持更多格式）
        r'(1[3-9]\d{9})',
        r'手机.*?[：:]\s*(1[3-9]\d{9})',
        r'电话.*?[：:]\s*(1[3-9]\d{9})',

        # QQ号格式
        r'QQ[：:]\s*([1-9]\d{4,})',
        r'qq[：:]\s*([1-9]\d{4,})',
        r'Q[：:]\s*([1-9]\d{4,})',

        # 直接的字母数字组合（在特定上下文中，优化：降低优先级）
        r'[：:]\s*([A-Za-z][A-Za-z0-9_.-]{4,})',

        # 特殊格式：@符号后的用户名
        r'@([A-Za-z][A-Za-z0-9_.-]{2,})',

        # 特殊格式：括号中的联系方式
        r'[（(]([A-Za-z0-9_.-]{5,})[）)]',
    ]

    # 尝试每个模式
    for pattern in patterns:
        matches = re.findall(pattern, signature, re.IGNORECASE)
        if matches:
            for match in matches:
                contact = match.strip()

                # 基本长度过滤
                if len(contact) < 3:
                    continue

                # 过滤掉明显不是联系方式的内容
                invalid_keywords = [
                    '合作', '商务', '联系', '咨询', '微信', '邮箱', '手机', '电话',
                    '作品', '视频', '内容', '分享', '生活', '美好', '随缘', '勿扰',
                    '专业', '服务', '爱好', '音乐', '电影', '导演', '指导'
                ]

                # 检查是否包含无效关键词
                if any(keyword in contact for keyword in invalid_keywords):
                    continue

                # 过滤掉纯数字但不是手机号或QQ号的情况
                if contact.isdigit():
                    # 手机号：11位，1开头
                    if len(contact) == 11 and contact.startswith('1'):
                        return contact
                    # QQ号：5-12位
                    elif 5 <= len(contact) <= 12:
                        return contact
                    # 其他纯数字跳过
                    else:
                        continue

                # 过滤掉太长的结果（可能是句子片段）
                if len(contact) > 30:
                    continue

                # 检查是否是有效的联系方式格式
                # 邮箱格式
                if '@' in contact and '.' in contact:
                    return contact

                # 包含字母和数字的组合（常见的用户名格式）
                if re.match(r'^[A-Za-z][A-Za-z0-9_.-]*$', contact):
                    return contact

                # 纯字母但长度合适
                if contact.isalpha() and 3 <= len(contact) <= 15:
                    return contact

    return ''

def get_token_from_db(db_path='system.db'):
    """从数据库获取最新的token"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        cursor.execute("""
            SELECT token FROM tokens
            ORDER BY create_time DESC
            LIMIT 1
        """)
        result = cursor.fetchone()
        conn.close()

        if result:
            return result[0]
        else:
            return None
    except Exception as e:
        print(f"❌ 从数据库获取token失败: {str(e)}")
        return None

def get_latest_token(db_path='system.db'):
    """
    从数据库获取最新的token
    """
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 查询最新的token
        cursor.execute("""
            SELECT token, create_time
            FROM tokens
            ORDER BY create_time DESC
            LIMIT 1
        """)
        result = cursor.fetchone()
        conn.close()

        if result:
            token, create_time = result
            print(f"🔑 获取最新token: {token[:20]}... (创建时间: {create_time})")
            return token
        else:
            print("❌ 数据库中没有找到token")
            return None

    except sqlite3.Error as e:
        print(f"❌ 从数据库获取token失败: {str(e)}")
        return None

# 获取个人信息蝉妈妈接口
def get_user_info(token):
    headers = {
        'origin': 'https://www.chanmama.com',
        'referer': 'https://www.chanmama.com/',
        'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Mobile/15E148 Safari/604.1',
        'x-client-hash': '4e75f486521cd9471142b5dd4ad628f72f616c4',
        'x-client-id': '342123125455',
        'x-client-version': '1',
        'x-encrypt-version': '2',
        'x-platform-id': '100000',
        'cookie': f'LOGIN-TOKEN-FORSNS={token}' if token else ''
    }
    url = 'https://passport.chanmama.com/v2/ucenter/user/info'
    response = requests.get(url, headers=headers)
    print(response.json())
    


# 登录禅妈妈接口
def login_cmm(username, password):
    """
    蝉妈妈登录
    """
    headers = {}

    # 将密码通过 md5 加密
    md5_hash = hashlib.md5()
    md5_hash.update(password.encode())
    hex_digest = md5_hash.hexdigest()


    json_data = {
        'from_platform': None,
        'appId': 10000,
        'timeStamp': int(time.time()),
        'username': username,
        'password': hex_digest
    }
    response = requests.post('https://api-service.chanmama.com/v1/access/token', headers=headers, json=json_data)

    # 检查响应是否成功
    if response.status_code == 200:
        response_data = response.json()

        # 获取token和登录状态
        token = response_data['data']['token']
        logged_in = response_data['data']['logged_in']

        print(f"🔑 获取到token: {token[:20]}...")
        print(f"📊 登录状态: {logged_in}")

        # 写入token到tokens表
        if logged_in and token:
            try:
                # 连接数据库
                conn = sqlite3.connect('system.db')
                cursor = conn.cursor()

                # 检查tokens表是否存在
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='tokens'")
                table_exists = cursor.fetchone() is not None

                if table_exists:
                    # 插入新token
                    create_time = datetime.now().isoformat()
                    cursor.execute(
                        "INSERT INTO tokens (token, create_time) VALUES (?, ?)",
                        (token, create_time)
                    )
                    conn.commit()
                    token_id = cursor.lastrowid

                    print(f"✅ Token已保存到数据库，ID: {token_id}")

                    conn.commit()
                else:
                    print("❌ tokens表不存在，请先初始化数据库")

                conn.close()

            except sqlite3.Error as e:
                print(f"❌ 保存token到数据库失败: {str(e)}")
            except Exception as e:
                print(f"❌ 处理token时发生错误: {str(e)}")
        else:
            print("❌ 登录失败或token为空，未保存到数据库")

        return response_data
    else:
        print(f"❌ 请求失败，状态码: {response.status_code}")
        return {"success": False, "message": f"HTTP {response.status_code}"}
if __name__ == "__main__":
    print("🧪 测试蝉妈妈功能...")

    # 1. 测试从数据库读取token
    print("\n1️⃣ 测试从数据库读取token...")
    db_token = get_token_from_db()
    if db_token:
        print(f"✅ 从数据库读取到token: {db_token[:20]}...")

        # 2. 测试API调用
        print("\n2️⃣ 测试API调用...")
        try:
            test_id = 'Te4oLu6PzddK8v0S_JURlE20CMuhagMW'
            result = get_real_info(test_id, db_token)
            # login_cmm('18775642907','weekseven')
            if result:
                print(f"✅ API调用成功: {str(result)}...")
            else:
                print("❌ API调用失败")
        except Exception as e:
            print(f"❌ API调用异常: {str(e)}")
    else:
        print("❌ 数据库中没有token，请先登录蝉妈妈")

    print("\n✅ 测试完成！")

