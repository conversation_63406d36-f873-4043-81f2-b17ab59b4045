#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终优化：进度显示和联系方式提取
"""

from cmm import extract_contact_code

def test_space_separated_contacts():
    """测试空格分隔的联系方式格式"""
    print("🧪 测试空格分隔的联系方式格式...")
    
    test_cases = [
        # 从图片中看到的实际案例
        {
            'signature': '合作 qiqiqyumi',
            'expected': 'qiqiqyumi',
            'description': '合作+空格+用户名'
        },
        {
            'signature': '商务 business123',
            'expected': 'business123',
            'description': '商务+空格+用户名'
        },
        {
            'signature': '商V contact888',
            'expected': 'contact888',
            'description': '商V+空格+用户名'
        },
        {
            'signature': '微信 wechat999',
            'expected': 'wechat999',
            'description': '微信+空格+用户名'
        },
        {
            'signature': 'VX user123',
            'expected': 'user123',
            'description': 'VX+空格+用户名'
        },
        {
            'signature': '联系 helper456',
            'expected': 'helper456',
            'description': '联系+空格+用户名'
        },
        {
            'signature': '咨询 support789',
            'expected': 'support789',
            'description': '咨询+空格+用户名'
        },
        {
            'signature': 'QQ 123456789',
            'expected': '123456789',
            'description': 'QQ+空格+号码'
        },
        
        # 复杂的真实案例
        {
            'signature': '🌸合作 flower.user123 🌸 专业带货',
            'expected': 'flower.user123',
            'description': '表情符号+合作+空格+用户名'
        },
        {
            'signature': '专业摄影师 商务 photo_master 作品展示',
            'expected': 'photo_master',
            'description': '前缀+商务+空格+用户名+后缀'
        },
        
        # 应该被过滤的情况
        {
            'signature': '合作',  # 纯关键词
            'expected': '',
            'description': '纯关键词（应过滤）'
        },
        {
            'signature': '商务',  # 纯关键词
            'expected': '',
            'description': '纯关键词（应过滤）'
        },
        {
            'signature': '合作 作品',  # 无效组合
            'expected': '',
            'description': '无效组合（应过滤）'
        }
    ]
    
    print(f"📋 开始测试 {len(test_cases)} 个空格分隔格式...")
    
    success_count = 0
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n🔍 测试用例 {i}: {case['description']}")
        print(f"   输入: '{case['signature']}'")
        
        try:
            result = extract_contact_code(case['signature'])
            expected = case['expected']
            
            print(f"   输出: '{result}'")
            print(f"   期望: '{expected}'")
            
            if result == expected:
                print(f"   ✅ 通过")
                success_count += 1
            else:
                print(f"   ❌ 失败")
                
        except Exception as e:
            print(f"   ❌ 异常: {str(e)}")
    
    print(f"\n📊 空格分隔格式测试结果:")
    print(f"   总用例: {len(test_cases)}")
    print(f"   通过: {success_count}")
    print(f"   失败: {len(test_cases) - success_count}")
    print(f"   成功率: {success_count/len(test_cases)*100:.1f}%")
    
    return success_count, len(test_cases)

def test_real_world_complex_cases():
    """测试真实世界的复杂案例"""
    print("\n🧪 测试真实世界复杂案例...")
    
    # 模拟从图片中看到的实际数据
    real_cases = [
        {
            'signature': '合作 qiqiqyumi',
            'expected': 'qiqiqyumi',
            'description': '图片案例1'
        },
        {
            'signature': '一定要看到最后哦🥰 商务合作：Yanyan1010666',
            'expected': 'Yanyan1010666',
            'description': '图片案例2'
        },
        {
            'signature': '商务💌 ：GLK88888GLK （非诚勿扰）@郭导生活号',
            'expected': 'GLK88888GLK',
            'description': '图片案例3'
        },
        {
            'signature': '🌸合作微信：flower123 🌸',
            'expected': 'flower123',
            'description': '表情符号+合作微信'
        },
        {
            'signature': '专业摄影师 商V photo_master 作品展示',
            'expected': 'photo_master',
            'description': '专业描述+商V+空格'
        },
        {
            'signature': '热爱生活 微信 life_lover 分享美好',
            'expected': 'life_lover',
            'description': '生活描述+微信+空格'
        },
        {
            'signature': '音乐创作者🎵 联系 music.creator 合作洽谈',
            'expected': 'music.creator',
            'description': '音乐创作+联系+空格'
        },
        {
            'signature': '电影导演 咨询 director_wang 专业指导',
            'expected': 'director_wang',
            'description': '电影导演+咨询+空格'
        },
        {
            'signature': '美食博主 VX food_blogger 美食分享',
            'expected': 'food_blogger',
            'description': '美食博主+VX+空格'
        },
        {
            'signature': '旅行达人✈️ 加我 travel_expert 旅行攻略',
            'expected': 'travel_expert',
            'description': '旅行达人+加我+空格'
        },
        
        # 应该无法提取的案例
        {
            'signature': '只是普通的个人简介，没有任何联系方式',
            'expected': '',
            'description': '无联系方式'
        },
        {
            'signature': '🎵音乐爱好者🎵 分享生活美好',
            'expected': '',
            'description': '纯描述性文字'
        },
        {
            'signature': '专业摄影师 作品展示 欢迎关注',
            'expected': '',
            'description': '专业描述无联系方式'
        }
    ]
    
    print(f"📋 测试 {len(real_cases)} 个真实复杂案例...")
    
    extracted_count = 0
    correct_count = 0
    
    for i, case in enumerate(real_cases, 1):
        print(f"\n📝 案例 {i}: {case['description']}")
        print(f"   原文: {case['signature']}")
        print(f"   期望: {case['expected'] if case['expected'] else '无'}")
        
        try:
            contact = extract_contact_code(case['signature'])
            if contact:
                print(f"   ✅ 提取到: {contact}")
                extracted_count += 1
                if contact == case['expected']:
                    correct_count += 1
                    print(f"   🎯 结果正确")
                else:
                    print(f"   ⚠️ 结果不符合期望")
            else:
                print(f"   ⚠️ 未提取到联系方式")
                if case['expected'] == '':
                    correct_count += 1
                    print(f"   🎯 正确（应该无联系方式）")
                
        except Exception as e:
            print(f"   ❌ 提取异常: {str(e)}")
    
    print(f"\n📊 真实案例统计:")
    print(f"   总案例: {len(real_cases)}")
    print(f"   成功提取: {extracted_count}")
    print(f"   结果正确: {correct_count}")
    print(f"   准确率: {correct_count/len(real_cases)*100:.1f}%")
    
    return correct_count, len(real_cases)

def test_progress_calculation():
    """测试进度计算逻辑"""
    print("\n🧪 测试进度计算逻辑...")
    
    # 模拟进度计算
    print("📊 模拟进度计算:")
    
    # 假设处理10个项目，每个间隔3秒
    total_items = 10
    sleep_interval = 3
    estimated_total_time = total_items * sleep_interval
    
    print(f"   总项目数: {total_items}")
    print(f"   间隔时间: {sleep_interval}秒")
    print(f"   预估总时间: {estimated_total_time}秒")
    
    # 模拟不同时间点的进度
    test_times = [0, 5, 10, 15, 20, 25, 30]
    
    for elapsed in test_times:
        percentage = min((elapsed / estimated_total_time) * 100, 95)
        current = round(percentage / 10)
        remaining_time = max(0, estimated_total_time - elapsed)
        
        if remaining_time > 60:
            time_str = f"{round(remaining_time / 60)}分钟"
        elif remaining_time > 0:
            time_str = f"{round(remaining_time)}秒"
        else:
            time_str = "即将完成"
        
        print(f"   {elapsed}秒: {percentage:.1f}% ({current}/{total_items}) - 剩余{time_str}")
    
    print("✅ 进度计算逻辑测试完成")
    return True

def run_all_tests():
    """运行所有测试"""
    print("🧪 运行最终优化测试...")
    print("=" * 60)
    
    # 测试空格分隔格式
    success1, total1 = test_space_separated_contacts()
    
    # 测试真实复杂案例
    success2, total2 = test_real_world_complex_cases()
    
    # 测试进度计算
    test_progress_calculation()
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 最终优化测试总结:")
    print(f"   空格分隔格式: {success1}/{total1} ({success1/total1*100:.1f}%)")
    print(f"   真实复杂案例: {success2}/{total2} ({success2/total2*100:.1f}%)")
    print(f"   综合成功率: {(success1+success2)/(total1+total2)*100:.1f}%")
    
    print(f"\n🎯 优化效果:")
    print("✅ 新增支持空格分隔格式（如：合作 qiqiqyumi）")
    print("✅ 优化过滤逻辑，减少误过滤")
    print("✅ 前端进度条和时间预估")
    print("✅ 提高联系方式提取准确率")
    
    if (success1+success2)/(total1+total2) >= 0.85:
        print("\n🎉 优化成功！联系方式提取准确率达到预期！")
    else:
        print("\n⚠️ 还需要进一步优化")

if __name__ == "__main__":
    run_all_tests()
