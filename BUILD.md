# 办公辅助系统 - 打包说明

## 📦 打包准备

### 1. 环境要求
- Python 3.7+
- pip包管理器

### 2. 安装依赖
```bash
pip install -r requirements.txt
pip install pyinstaller
```

### 3. 核心依赖
- `pywebview>=4.0.0` - GUI框架
- `openpyxl>=3.1.0` - Excel文件处理
- `requests>=2.28.0` - HTTP请求
- `pyinstaller` - 打包工具

## 🚀 打包方法

### 方法1: 使用打包脚本（推荐）
```bash
python build.py
```

### 方法2: 手动打包
```bash
# 清理旧文件
rm -rf build dist *.spec

# 打包命令
pyinstaller --onefile --windowed --name=办公辅助系统 --add-data=web;web --clean main.py
```

### 方法3: 开发模式运行
```bash
# Windows
start.bat

# Linux/Mac
chmod +x start.sh
./start.sh
```

## 📁 文件结构

```
办公辅助系统/
├── main.py              # 主程序入口
├── apis.py               # API接口
├── cmm.py               # 蝉妈妈相关功能
├── sqlite3_util.py     # 数据库工具
├── web/                 # 前端文件
│   └── pages/
│       └── talent.html  # 主界面
├── requirements.txt     # 依赖列表
├── build.py            # 打包脚本
├── start.bat           # Windows启动脚本
├── start.sh            # Linux/Mac启动脚本
└── BUILD.md            # 打包说明
```

## ⚙️ 打包配置

### PyInstaller参数说明
- `--onefile`: 打包成单个exe文件
- `--windowed`: 无控制台窗口
- `--name`: 指定程序名称
- `--add-data`: 添加数据文件
- `--hidden-import`: 隐式导入模块
- `--clean`: 清理临时文件

### 优化建议
1. **减小文件大小**:
   - 使用`--exclude-module`排除不需要的模块
   - 使用UPX压缩（可选）

2. **提高启动速度**:
   - 使用`--onedir`模式（文件夹形式）
   - 预编译Python模块

3. **兼容性**:
   - 在目标系统上测试
   - 检查依赖库版本

## 🐛 常见问题

### 1. 打包失败
- 检查Python版本（需要3.7+）
- 确保所有依赖已安装
- 检查文件路径是否正确

### 2. 运行时错误
- 检查web目录是否正确打包
- 确保数据库文件存在
- 检查网络连接

### 3. 文件过大
- 排除不必要的模块
- 使用虚拟环境减少依赖
- 考虑使用压缩工具

## 📋 发布清单

打包完成后检查：
- [ ] 程序能正常启动
- [ ] 界面显示正常
- [ ] Excel文件处理功能正常
- [ ] 数据库操作正常
- [ ] 蝉妈妈登录功能正常
- [ ] 导出功能正常
- [ ] 控制台输出正常

## 🎯 部署建议

1. **单机部署**: 直接运行exe文件
2. **网络部署**: 配置共享数据库
3. **批量部署**: 使用安装包制作工具

## 📞 技术支持

如遇到打包问题，请检查：
1. Python环境配置
2. 依赖库版本兼容性
3. 系统权限设置
4. 防火墙/杀毒软件设置
