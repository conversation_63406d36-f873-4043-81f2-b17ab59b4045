#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试前端调用update_processing_progress
"""

def test_update_processing_progress_api():
    """测试update_processing_progress API"""
    print("🧪 测试update_processing_progress API...")
    
    try:
        from apis import API, add_console_log, update_console_status
        
        api = API()
        
        # 先添加一些测试日志
        add_console_log("🚀 开始处理Excel文件...", "info")
        add_console_log("📄 读取文件: test.xlsx", "info")
        add_console_log("📊 开始解析Excel文件结构", "info")
        
        # 更新状态
        update_console_status(
            status="processing",
            message="正在处理文件...",
            progress=30,
            is_processing=True
        )
        
        # 测试API调用
        result = api.update_processing_progress()
        
        print("✅ update_processing_progress调用成功")
        print(f"📊 返回数据类型: {type(result)}")
        
        if isinstance(result, dict):
            print(f"📊 包含字段: {list(result.keys())}")
            print(f"📊 日志数量: {len(result.get('logs', []))}")
            print(f"📊 当前状态: {result.get('current_status', 'unknown')}")
            print(f"📊 当前消息: {result.get('current_message', 'unknown')}")
            print(f"📊 进度: {result.get('progress', 0)}%")
            print(f"📊 是否处理中: {result.get('is_processing', False)}")
            
            # 显示日志
            if result.get('logs'):
                print("📋 控制台日志:")
                for i, log in enumerate(result['logs'], 1):
                    print(f"   {i}. [{log['time_str']}] {log['type']}: {log['message']}")
            
            return True
        else:
            print(f"❌ 返回数据格式错误: {result}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_frontend_simulation():
    """模拟前端调用"""
    print("\n🧪 模拟前端调用...")
    
    try:
        from apis import API, add_console_log, update_console_status
        import time
        
        api = API()
        
        # 模拟处理过程
        steps = [
            ("🚀 开始处理Excel文件...", "info", 10),
            ("📄 读取文件: test.xlsx", "info", 20),
            ("📊 开始解析Excel文件结构", "info", 30),
            ("🔍 开始扫描数据和超链接", "info", 40),
            ("🔗 发现 5 个超链接，开始获取达人信息", "success", 50),
            ("📡 [1/5] 获取达人: user123", "info", 60),
            ("✅ 获取成功: user123", "success", 70),
            ("📞 提取联系方式: 微信123", "success", 75),
            ("⏱️ 休眠 3 秒，避免请求过快", "warning", 80),
            ("✅ 完成第 1 个达人信息获取", "success", 85),
            ("💾 开始准备数据库插入", "info", 90),
            ("✅ 成功插入 5 条数据到数据库", "success", 95),
            ("🎉 Excel文件处理完成！所有达人信息已获取", "success", 100),
        ]
        
        print("📱 模拟前端定时调用update_processing_progress...")
        
        for i, (message, log_type, progress) in enumerate(steps):
            # 模拟后端处理步骤
            add_console_log(message, log_type)
            update_console_status(
                status="processing" if progress < 100 else "completed",
                message=f"处理进度 {progress}%",
                progress=progress,
                is_processing=progress < 100
            )
            
            # 模拟前端调用
            result = api.update_processing_progress()
            
            print(f"\n📱 前端第{i+1}次调用:")
            print(f"   📊 状态: {result.get('current_status', 'unknown')}")
            print(f"   📊 消息: {result.get('current_message', 'unknown')}")
            print(f"   📊 进度: {result.get('progress', 0)}%")
            print(f"   📊 处理中: {result.get('is_processing', False)}")
            print(f"   📊 日志数: {len(result.get('logs', []))}")
            
            if result.get('logs'):
                latest_log = result['logs'][-1]
                print(f"   📋 最新日志: [{latest_log['time_str']}] {latest_log['message']}")
            
            time.sleep(0.5)  # 模拟间隔
        
        print("\n✅ 前端调用模拟完成")
        return True
        
    except Exception as e:
        print(f"❌ 前端调用模拟失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def show_frontend_integration():
    """显示前端集成说明"""
    print("\n" + "="*60)
    print("📱 前端集成说明")
    print("="*60)
    
    print("\n✅ 前端现在的调用流程:")
    print("1. 📱 页面加载时启动定时器:")
    print("   setInterval(() => {")
    print("       fetchConsoleOutput();")
    print("   }, 1000); // 每秒调用一次")
    
    print("\n2. 📡 fetchConsoleOutput函数:")
    print("   const output = await api.update_processing_progress();")
    print("   consoleOutput.value = output;")
    print("   // 自动滚动到底部")
    
    print("\n3. 🎨 工作状态卡片显示:")
    print("   <div v-for=\"log in consoleOutput.logs.slice(-10)\">")
    print("       [{{ log.time_str }}] {{ log.message }}")
    print("   </div>")
    
    print("\n✅ 数据流向:")
    print("后端process_file → add_console_log → GLOBAL_CONSOLE_OUTPUT")
    print("前端定时器 → update_processing_progress → 返回GLOBAL_CONSOLE_OUTPUT")
    print("前端UI → 显示实时控制台输出")
    
    print("\n🎯 用户看到的效果:")
    print("工作状态卡片中实时显示:")
    print("14:30:15 🚀 开始处理Excel文件...")
    print("14:30:16 📄 读取文件: test.xlsx")
    print("14:30:17 📊 开始解析Excel文件结构")
    print("14:30:18 🔍 开始扫描数据和超链接")
    print("14:30:19 🔗 发现 5 个超链接")
    print("14:30:20 📡 [1/5] 获取达人: user123")
    print("14:30:21 ✅ 获取成功: user123")
    print("14:30:22 📞 提取联系方式: 微信123")
    print("14:30:23 ⏱️ 休眠 3 秒")
    print("14:30:26 ✅ 完成第 1 个达人信息获取")

def run_all_tests():
    """运行所有测试"""
    print("🧪 测试前端调用update_processing_progress")
    print("="*60)
    
    tests = [
        ("update_processing_progress API测试", test_update_processing_progress_api),
        ("前端调用模拟测试", test_frontend_simulation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"{'✅' if result else '❌'} {test_name}: {'通过' if result else '失败'}")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {str(e)}")
            results.append((test_name, False))
    
    # 显示前端集成说明
    show_frontend_integration()
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 测试总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {status} {test_name}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有测试通过！前端现在正确调用update_processing_progress！")
        print("\n💡 现在前端会:")
        print("   1. 每秒调用api.update_processing_progress()")
        print("   2. 获取实时控制台输出")
        print("   3. 在工作状态卡片中显示")
        print("   4. 自动滚动到最新日志")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    run_all_tests()
