#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试联系方式提取功能
"""

from cmm import extract_contact_code

def test_contact_extraction():
    """测试联系方式提取"""
    print("🧪 测试联系方式提取功能...")
    
    # 测试用例
    test_cases = [
        {
            'signature': '商务🌷合作：Yanyan1010666 我想把Yanyan1010666 提取出来',
            'expected': 'Yanyan1010666',
            'description': '商务合作格式'
        },
        {
            'signature': '商务💌 ：GLK88888GLK （非诚勿扰）@郭导生活号',
            'expected': 'GLK88888GLK',
            'description': '商务合作格式2'
        },
        {
            'signature': '微信：test123456 欢迎咨询',
            'expected': 'test123456',
            'description': '微信格式'
        },
        {
            'signature': 'VX：contact888 联系我',
            'expected': 'contact888',
            'description': 'VX格式'
        },
        {
            'signature': '合作V：abc123def',
            'expected': 'abc123def',
            'description': '合作V格式'
        },
        {
            'signature': '联系方式：user999888',
            'expected': 'user999888',
            'description': '联系方式格式'
        },
        {
            'signature': '咨询：help123',
            'expected': 'help123',
            'description': '咨询格式'
        },
        {
            'signature': '邮箱：<EMAIL>',
            'expected': '<EMAIL>',
            'description': '邮箱格式'
        },
        {
            'signature': '手机：13812345678',
            'expected': '13812345678',
            'description': '手机号格式'
        },
        {
            'signature': '一切随缘就好🌹🌹 没有联系方式',
            'expected': '',
            'description': '无联系方式'
        },
        {
            'signature': '',
            'expected': '',
            'description': '空字符串'
        },
        {
            'signature': None,
            'expected': '',
            'description': 'None值'
        }
    ]
    
    print(f"📋 开始测试 {len(test_cases)} 个用例...")
    
    success_count = 0
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n🔍 测试用例 {i}: {case['description']}")
        print(f"   输入: {case['signature']}")
        
        try:
            result = extract_contact_code(case['signature'])
            expected = case['expected']
            
            print(f"   输出: {result}")
            print(f"   期望: {expected}")
            
            if result == expected:
                print(f"   ✅ 通过")
                success_count += 1
            else:
                print(f"   ❌ 失败")
                
        except Exception as e:
            print(f"   ❌ 异常: {str(e)}")
    
    print(f"\n📊 测试结果:")
    print(f"   总用例: {len(test_cases)}")
    print(f"   通过: {success_count}")
    print(f"   失败: {len(test_cases) - success_count}")
    print(f"   成功率: {success_count/len(test_cases)*100:.1f}%")
    
    return success_count == len(test_cases)

def test_real_signatures():
    """测试真实的signature数据"""
    print("\n🧪 测试真实signature数据...")
    
    # 模拟真实的signature数据
    real_signatures = [
        '一切随缘就好🌹🌹 商V ：heisaomaiyu',
        '商务💌 ：GLK88888GLK （非诚勿扰）@郭导生活号 电影《巨齿鲨1》中方动作指导 参与作品百余部',
        '🌸合作微信：flower123 🌸',
        '联系方式VX：contact2024',
        '商务合作：business888 其他勿扰',
        '咨询V：help999 专业服务',
        '工作邮箱：<EMAIL>',
        '手机号：***********',
        '只是普通的个人简介，没有任何联系方式',
        '🎵音乐爱好者🎵 分享生活美好',
    ]
    
    print(f"📋 测试 {len(real_signatures)} 个真实signature...")
    
    for i, signature in enumerate(real_signatures, 1):
        print(f"\n📝 Signature {i}:")
        print(f"   原文: {signature}")
        
        try:
            contact = extract_contact_code(signature)
            if contact:
                print(f"   ✅ 提取到: {contact}")
            else:
                print(f"   ⚠️ 未提取到联系方式")
                
        except Exception as e:
            print(f"   ❌ 提取异常: {str(e)}")
    
    return True

def test_edge_cases():
    """测试边界情况"""
    print("\n🧪 测试边界情况...")
    
    edge_cases = [
        {
            'signature': '商务：a',  # 太短
            'description': '联系方式太短'
        },
        {
            'signature': '商务：ab',  # 太短
            'description': '联系方式太短2'
        },
        {
            'signature': '商务：abc',  # 刚好3位
            'description': '联系方式刚好3位'
        },
        {
            'signature': '商务：123456789012345678901234567890',  # 很长
            'description': '联系方式很长'
        },
        {
            'signature': '商务：<EMAIL>',  # 复杂邮箱
            'description': '复杂邮箱格式'
        },
        {
            'signature': '商务合作：user_name-123',  # 包含下划线和连字符
            'description': '包含特殊字符'
        },
        {
            'signature': '多个：contact1 还有：contact2',  # 多个联系方式
            'description': '多个联系方式（应返回第一个）'
        }
    ]
    
    print(f"📋 测试 {len(edge_cases)} 个边界情况...")
    
    for i, case in enumerate(edge_cases, 1):
        print(f"\n🔍 边界测试 {i}: {case['description']}")
        print(f"   输入: {case['signature']}")
        
        try:
            result = extract_contact_code(case['signature'])
            print(f"   输出: {result}")
            print(f"   长度: {len(result) if result else 0}")
            
        except Exception as e:
            print(f"   ❌ 异常: {str(e)}")
    
    return True

if __name__ == "__main__":
    print("🧪 联系方式提取功能测试...")
    
    # 运行所有测试
    test_contact_extraction()
    test_real_signatures()
    test_edge_cases()
    
    print("\n✅ 所有测试完成！")
    
    # 显示正则表达式说明
    print("\n📋 支持的联系方式格式:")
    formats = [
        "商务合作：用户名",
        "微信：用户名", 
        "VX：用户名",
        "V：用户名",
        "联系方式：用户名",
        "咨询：用户名",
        "邮箱格式：<EMAIL>",
        "手机号：13812345678"
    ]
    
    for fmt in formats:
        print(f"   • {fmt}")
    
    print("\n📝 提取规则:")
    print("   • 联系方式长度至少3位")
    print("   • 支持字母、数字、下划线、连字符")
    print("   • 多个联系方式时返回第一个")
    print("   • 不区分大小写")
