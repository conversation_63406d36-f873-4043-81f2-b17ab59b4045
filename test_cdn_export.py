#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试CDN库加载和导出功能
"""

import webbrowser
import os
import time

def create_test_page():
    """创建测试页面验证CDN库"""
    html_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CDN库测试</title>
    
    <!-- 文件处理库 CDN -->
    <script src="https://unpkg.com/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <script src="https://unpkg.com/file-saver@2.0.5/dist/FileSaver.min.js"></script>
    
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { color: #333; text-align: center; }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 CDN库加载测试</h1>
        
        <div class="test-section">
            <h3>📚 库加载检查</h3>
            <button onclick="checkLibraries()">检查库加载状态</button>
            <div id="libraryLog" class="log"></div>
        </div>

        <div class="test-section">
            <h3>📄 CSV导出测试</h3>
            <button onclick="testCSVExport()">测试CSV导出</button>
            <div id="csvLog" class="log"></div>
        </div>

        <div class="test-section">
            <h3>📊 Excel导出测试</h3>
            <button onclick="testExcelExport()">测试Excel导出</button>
            <div id="excelLog" class="log"></div>
        </div>

        <div class="test-section">
            <h3>🔧 pywebview环境测试</h3>
            <button onclick="testPywebviewEnv()">检查pywebview环境</button>
            <div id="pywebviewLog" class="log"></div>
        </div>
    </div>

    <script>
        function log(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            container.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            container.scrollTop = container.scrollHeight;
        }

        function checkLibraries() {
            log('libraryLog', '🔍 检查CDN库加载状态...');
            
            // 检查FileSaver
            if (typeof saveAs !== 'undefined') {
                log('libraryLog', '✅ FileSaver.js 加载成功', 'success');
                log('libraryLog', `   版本信息: ${saveAs.toString().substring(0, 50)}...`);
            } else {
                log('libraryLog', '❌ FileSaver.js 未加载', 'error');
            }
            
            // 检查XLSX
            if (typeof XLSX !== 'undefined') {
                log('libraryLog', '✅ XLSX.js 加载成功', 'success');
                log('libraryLog', `   版本: ${XLSX.version || '未知'}`);
                log('libraryLog', `   支持格式: ${Object.keys(XLSX.read).length > 0 ? '多种格式' : '基本格式'}`);
            } else {
                log('libraryLog', '❌ XLSX.js 未加载', 'error');
            }
            
            // 检查Blob支持
            if (typeof Blob !== 'undefined') {
                log('libraryLog', '✅ Blob API 支持', 'success');
            } else {
                log('libraryLog', '❌ Blob API 不支持', 'error');
            }
            
            // 检查URL.createObjectURL支持
            if (typeof URL !== 'undefined' && typeof URL.createObjectURL === 'function') {
                log('libraryLog', '✅ URL.createObjectURL 支持', 'success');
            } else {
                log('libraryLog', '❌ URL.createObjectURL 不支持', 'error');
            }
        }

        function testCSVExport() {
            log('csvLog', '🚀 开始测试CSV导出...');
            
            if (typeof saveAs === 'undefined') {
                log('csvLog', '❌ FileSaver.js 未加载，无法测试', 'error');
                return;
            }
            
            try {
                // 创建测试数据
                const testData = [
                    ['ID', '用户昵称', '抖音ID', '蝉妈妈ID', '简介', '联系方式', '来源文件', '创建时间'],
                    ['1', '测试用户1', 'user1', 'cmm1', '商务合作：contact1', 'contact1', 'test1.xlsx', '2024-01-01 12:00:00'],
                    ['2', '测试用户2', 'user2', 'cmm2', '微信：wx123', 'wx123', 'test2.xlsx', '2024-01-01 12:01:00'],
                    ['3', '测试用户3', 'user3', 'cmm3', '普通简介，没有联系方式', '', 'test3.xlsx', '2024-01-01 12:02:00']
                ];
                
                // 生成CSV内容
                let csvContent = '';
                testData.forEach(row => {
                    const processedRow = row.map(field => {
                        const str = String(field || '');
                        if (str.includes(',') || str.includes('"') || str.includes('\\n')) {
                            return '"' + str.replace(/"/g, '""') + '"';
                        }
                        return str;
                    });
                    csvContent += processedRow.join(',') + '\\n';
                });
                
                // 添加BOM
                const BOM = '\\uFEFF';
                csvContent = BOM + csvContent;
                
                log('csvLog', `📊 生成CSV内容: ${csvContent.length} 字符`);
                
                // 创建Blob
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                log('csvLog', `📦 创建Blob: ${blob.size} 字节`);
                
                // 使用FileSaver下载
                const fileName = `测试数据_${new Date().toISOString().slice(0, 16).replace(/[-:T]/g, '')}.csv`;
                saveAs(blob, fileName);
                
                log('csvLog', `✅ CSV导出成功: ${fileName}`, 'success');
                
            } catch (error) {
                log('csvLog', `❌ CSV导出失败: ${error.message}`, 'error');
            }
        }

        function testExcelExport() {
            log('excelLog', '🚀 开始测试Excel导出...');
            
            if (typeof XLSX === 'undefined') {
                log('excelLog', '❌ XLSX.js 未加载，无法测试', 'error');
                return;
            }
            
            try {
                // 创建测试数据
                const testData = [
                    {
                        'ID': 1,
                        '用户昵称': '测试用户1',
                        '抖音ID': 'user1',
                        '蝉妈妈ID': 'cmm1',
                        '简介': '商务合作：contact1',
                        '联系方式': 'contact1',
                        '来源文件': 'test1.xlsx',
                        '创建时间': '2024-01-01 12:00:00'
                    },
                    {
                        'ID': 2,
                        '用户昵称': '测试用户2',
                        '抖音ID': 'user2',
                        '蝉妈妈ID': 'cmm2',
                        '简介': '微信：wx123',
                        '联系方式': 'wx123',
                        '来源文件': 'test2.xlsx',
                        '创建时间': '2024-01-01 12:01:00'
                    }
                ];
                
                log('excelLog', `📊 准备导出数据: ${testData.length} 行`);
                
                // 创建工作簿
                const wb = XLSX.utils.book_new();
                const ws = XLSX.utils.json_to_sheet(testData);
                
                // 设置列宽
                const colWidths = [
                    { wch: 8 },   // ID
                    { wch: 20 },  // 用户昵称
                    { wch: 20 },  // 抖音ID
                    { wch: 30 },  // 蝉妈妈ID
                    { wch: 50 },  // 简介
                    { wch: 20 },  // 联系方式
                    { wch: 15 },  // 来源文件
                    { wch: 20 }   // 创建时间
                ];
                ws['!cols'] = colWidths;
                
                XLSX.utils.book_append_sheet(wb, ws, '用户数据');
                
                log('excelLog', '📦 创建工作簿成功');
                
                // 生成文件名
                const fileName = `测试数据_${new Date().toISOString().slice(0, 16).replace(/[-:T]/g, '')}.xlsx`;
                
                // 导出文件
                XLSX.writeFile(wb, fileName);
                
                log('excelLog', `✅ Excel导出成功: ${fileName}`, 'success');
                
            } catch (error) {
                log('excelLog', `❌ Excel导出失败: ${error.message}`, 'error');
            }
        }

        function testPywebviewEnv() {
            log('pywebviewLog', '🔍 检查pywebview环境...');
            
            // 检查pywebview API
            if (window.pywebview) {
                log('pywebviewLog', '✅ pywebview 对象存在', 'success');
                
                if (window.pywebview.api) {
                    log('pywebviewLog', '✅ pywebview.api 可用', 'success');
                } else {
                    log('pywebviewLog', '⚠️ pywebview.api 不可用', 'error');
                }
            } else {
                log('pywebviewLog', '❌ 不在pywebview环境中', 'error');
            }
            
            // 检查用户代理
            log('pywebviewLog', `🌐 用户代理: ${navigator.userAgent}`);
            
            // 检查下载权限
            if (document.createElement('a').download !== undefined) {
                log('pywebviewLog', '✅ 支持download属性', 'success');
            } else {
                log('pywebviewLog', '❌ 不支持download属性', 'error');
            }
            
            // 检查文件API
            if (typeof File !== 'undefined' && typeof FileReader !== 'undefined') {
                log('pywebviewLog', '✅ File API 支持', 'success');
            } else {
                log('pywebviewLog', '❌ File API 不支持', 'error');
            }
        }

        // 页面加载时自动检查
        window.onload = function() {
            setTimeout(() => {
                checkLibraries();
            }, 1000);
        };
    </script>
</body>
</html>'''
    
    # 保存测试页面
    test_file = 'test_cdn_libraries.html'
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    return test_file

def run_test():
    """运行CDN库测试"""
    print("🧪 创建CDN库测试页面...")
    
    # 创建测试页面
    test_file = create_test_page()
    print(f"✅ 测试页面已创建: {test_file}")
    
    # 获取绝对路径
    abs_path = os.path.abspath(test_file)
    file_url = f"file:///{abs_path.replace(os.sep, '/')}"
    
    print(f"🌐 测试页面URL: {file_url}")
    
    # 打开测试页面
    print("🚀 正在打开测试页面...")
    try:
        webbrowser.open(file_url)
        print("✅ 测试页面已在浏览器中打开")
    except Exception as e:
        print(f"❌ 打开浏览器失败: {str(e)}")
        print(f"请手动打开: {file_url}")
    
    print("\n📋 测试说明:")
    print("1. 页面会自动检查CDN库加载状态")
    print("2. 点击各个测试按钮验证功能")
    print("3. 如果CSV/Excel导出成功，文件会下载到默认下载文件夹")
    print("4. 检查pywebview环境信息")
    
    print("\n💡 如果测试成功，说明CDN库可以正常使用")
    print("💡 如果测试失败，可能需要检查网络连接或使用本地库文件")
    
    # 等待用户测试
    input("\n按回车键继续...")
    
    # 清理测试文件
    try:
        os.remove(test_file)
        print(f"🧹 测试文件已清理: {test_file}")
    except:
        pass

if __name__ == "__main__":
    run_test()
