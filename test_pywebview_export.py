#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试pywebview环境下的文件导出功能
"""

from apis import API
import tempfile
import os

def test_csv_save():
    """测试CSV文件保存"""
    print("🧪 测试CSV文件保存功能...")
    
    # 创建API实例
    api = API()
    
    # 模拟CSV内容
    csv_content = """ID,用户昵称,抖音ID,蝉妈妈ID,简介,联系方式,来源文件,创建时间
1,测试用户1,user1,cmm1,商务合作：contact1,contact1,test1.xlsx,2024-01-01 12:00:00
2,测试用户2,user2,cmm2,微信：wx123,wx123,test2.xlsx,2024-01-01 12:01:00
3,测试用户3,user3,cmm3,普通简介，没有联系方式,,test3.xlsx,2024-01-01 12:02:00"""
    
    file_name = "测试数据_20240802_1400.csv"
    
    print(f"📄 CSV内容长度: {len(csv_content)} 字符")
    print(f"📁 文件名: {file_name}")
    
    # 测试保存功能
    try:
        result = api.save_export_file(csv_content, file_name, 'csv')
        
        print(f"📊 保存结果:")
        print(f"   成功: {result.get('success', False)}")
        print(f"   消息: {result.get('message', '无')}")
        
        if result.get('success'):
            print(f"   文件路径: {result.get('file_path', '无')}")
            print(f"   文件大小: {result.get('file_size', 0)} 字节")
            
            # 验证文件内容
            file_path = result.get('file_path')
            if file_path and os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8-sig') as f:
                    saved_content = f.read()
                
                print(f"✅ 文件验证成功")
                print(f"   保存的内容长度: {len(saved_content)} 字符")
                print(f"   内容匹配: {saved_content.strip() == csv_content.strip()}")
                
                return True
            else:
                print(f"❌ 文件不存在: {file_path}")
                return False
        else:
            print(f"❌ 保存失败: {result.get('message')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        return False

def test_excel_save():
    """测试Excel文件保存"""
    print("\n🧪 测试Excel文件保存功能...")
    
    # 创建API实例
    api = API()
    
    # 模拟Excel文件的Base64内容（简化版本）
    # 这里使用一个简单的测试内容
    test_content = "这是一个测试Excel文件内容"
    import base64
    base64_content = base64.b64encode(test_content.encode('utf-8')).decode('utf-8')
    
    file_name = "测试数据_20240802_1400.xlsx"
    
    print(f"📊 Base64内容长度: {len(base64_content)} 字符")
    print(f"📁 文件名: {file_name}")
    
    # 测试保存功能
    try:
        result = api.save_excel_file(base64_content, file_name)
        
        print(f"📊 保存结果:")
        print(f"   成功: {result.get('success', False)}")
        print(f"   消息: {result.get('message', '无')}")
        
        if result.get('success'):
            print(f"   文件路径: {result.get('file_path', '无')}")
            print(f"   文件大小: {result.get('file_size', 0)} 字节")
            
            # 验证文件内容
            file_path = result.get('file_path')
            if file_path and os.path.exists(file_path):
                with open(file_path, 'rb') as f:
                    saved_data = f.read()
                
                # 解码验证
                decoded_content = base64.b64decode(base64_content)
                
                print(f"✅ 文件验证成功")
                print(f"   保存的数据长度: {len(saved_data)} 字节")
                print(f"   数据匹配: {saved_data == decoded_content}")
                
                return True
            else:
                print(f"❌ 文件不存在: {file_path}")
                return False
        else:
            print(f"❌ 保存失败: {result.get('message')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        return False

def test_tkinter_availability():
    """测试tkinter可用性"""
    print("\n🧪 测试tkinter可用性...")
    
    try:
        import tkinter as tk
        from tkinter import filedialog
        
        print("✅ tkinter导入成功")
        
        # 测试创建隐藏窗口
        root = tk.Tk()
        root.withdraw()
        print("✅ 隐藏窗口创建成功")
        
        # 测试置顶属性
        root.attributes('-topmost', True)
        print("✅ 置顶属性设置成功")
        
        root.destroy()
        print("✅ 窗口销毁成功")
        
        print("✅ tkinter功能完全可用")
        return True
        
    except Exception as e:
        print(f"❌ tkinter测试失败: {str(e)}")
        return False

def show_solution_summary():
    """显示解决方案总结"""
    print("\n" + "="*60)
    print("📋 pywebview文件下载解决方案总结")
    print("="*60)
    
    print("\n🔍 问题分析:")
    print("❌ pywebview内嵌浏览器不支持文件下载对话框")
    print("❌ FileSaver.js在pywebview环境中无法正常工作")
    print("❌ 浏览器的saveAs功能被限制")
    
    print("\n✅ 解决方案:")
    print("1. 🐍 使用Python后端处理文件保存")
    print("2. 🖥️ 通过tkinter显示文件保存对话框")
    print("3. 💾 直接写入用户选择的文件路径")
    print("4. 🔄 保留浏览器下载作为备用方案")
    
    print("\n🔧 技术实现:")
    print("• CSV文件: 前端生成内容 → Python后端保存")
    print("• Excel文件: 前端生成Base64 → Python解码保存")
    print("• 文件对话框: tkinter.filedialog.asksaveasfilename")
    print("• 编码处理: UTF-8-sig (带BOM) for CSV")
    
    print("\n📱 用户体验:")
    print("✅ 点击导出 → 选择格式 → 确认导出")
    print("✅ 弹出文件保存对话框 → 选择保存位置")
    print("✅ 文件直接保存到指定位置")
    print("✅ 显示保存成功消息和文件路径")
    
    print("\n🎯 优势:")
    print("• 完全兼容pywebview环境")
    print("• 用户可以选择保存位置")
    print("• 支持CSV和Excel两种格式")
    print("• 有备用方案保证兼容性")
    print("• 提供详细的操作反馈")

def run_all_tests():
    """运行所有测试"""
    print("🧪 测试pywebview环境下的文件导出功能")
    print("="*60)
    
    tests = [
        ("tkinter可用性测试", test_tkinter_availability),
        ("CSV文件保存测试", test_csv_save),
        ("Excel文件保存测试", test_excel_save),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"{'✅' if result else '❌'} {test_name}: {'通过' if result else '失败'}")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {str(e)}")
            results.append((test_name, False))
    
    # 显示解决方案总结
    show_solution_summary()
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 测试总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {status} {test_name}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有测试通过！pywebview文件导出功能已就绪！")
        print("\n💡 现在可以在主应用中测试导出功能:")
        print("   1. 点击导出按钮")
        print("   2. 选择CSV或Excel格式")
        print("   3. 在弹出的对话框中选择保存位置")
        print("   4. 文件将直接保存到指定位置")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    run_all_tests()
